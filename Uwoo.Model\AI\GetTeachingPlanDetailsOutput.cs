﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取教案详情输出
    /// </summary>
    public class GetTeachingPlanDetailsOutput
    {
        /// <summary>
        /// 教案Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 教案名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 类型（1:文本、2:Word）
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 教案文本内容
        /// </summary>
        public string? Text { get; set; }
    }
}
