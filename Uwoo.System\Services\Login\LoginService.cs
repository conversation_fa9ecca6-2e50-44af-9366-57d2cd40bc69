﻿using Dm.filter.log;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.DBManager;
using Uwoo.Core.Enums;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.Services;
using Uwoo.Core.Utilities;
using Uwoo.Entity.DomainModels;
using Uwoo.Model.CustomException;
using Uwoo.Model.JWT;
using Uwoo.Model.Login;
using Uwoo.System.IRepositories;
using Uwoo.System.IRepositories.Login;
using Uwoo.System.IServices;
using Uwoo.System.IServices.Login;
using static Dapper.SqlMapper;

namespace Uwoo.System.Services.Login
{
    public class LoginService : ServiceBase<Base_User, ILoginRepository>, ILoginService, IDependency
    {
        private Microsoft.AspNetCore.Http.HttpContext _context;
        public LoginService(IHttpContextAccessor httpContextAccessor)
        {
            _context = httpContextAccessor.HttpContext;
        }

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public SubmitLoginOutput SubmitLogin(SubmitLoginInput input)
        {
            try
            {
                //if (input.Type == 0)
                //{
                //    //获取学生信息
                //    Base_User student = DBSqlSugar.Queryable<Base_User>().Where(p => p.UserName == input.PhoneNo && p.Deleted == false).First();
                //    if (student == null)
                //    {
                //        student = DBSqlSugar.Queryable<Base_User>().Where(p => p.PhoneNo == input.PhoneNo && p.Deleted == false).First();
                //        if (student == null)
                //        {
                return new SubmitLoginOutput()
                {
                    IsLogin = false,
                    Msg = "登录不可用"
                };
                //        }
                //    }
                //    //验证密码
                //    if (input.Password.Trim().EncryptDES(AppSetting.Secret.User) != (student.PassWord ?? ""))
                //    {
                //        return new SubmitLoginOutput()
                //        {
                //            IsLogin = false,
                //            Msg = "密码不正确!"
                //        };
                //    }
                //    //获取学生信息
                //    Sys_UserAssociation_Mapping studentInfoId = DBSqlSugar.Queryable<Sys_UserAssociation_Mapping>().Where(p => p.UserId == student.Id && p.UserType == 1 && p.Deleted == false).First();
                //    if (studentInfoId == null)
                //    {
                //        return new SubmitLoginOutput()
                //        {
                //            IsLogin = false,
                //            Msg = "当前用户未绑定学生信息，请联系管理员!"
                //        };
                //    }

                //    // Exam_Student state = DBSqlSugar.QueryList<Exam_Student>($@"SELECT s.* FROM Base_User b INNER JOIN  Sys_UserAssociation_Mapping m ON b.Id=m.UserID INNER JOIN Exam_Student s ON s.Id=m.RassociationId where b.id=@ID",new {id= student.Id }).FirstOrDefault();
                //    //目前不需要验证审核状态
                //    ////验证账号状态
                //    //if (state.Status == 1)
                //    //{
                //    //    return new SubmitLoginOutput()
                //    //    {
                //    //        IsLogin = false,
                //    //        Msg = "您的帐号注册信息在审核中，待审核通过即可登录！"
                //    //    };
                //    //}
                //    //if (student.UserState == 3)
                //    //{
                //    //    //获取审核记录
                //    //    List<string> userAuditRecords = DBSqlSugar.Queryable<Sys_UserAuditRecord>()
                //    //        .Where(p => p.UserId == student.Id && p.UserType == 1 && p.AuditState == 2)
                //    //        .OrderByDescending(p => p.CreateTime).Select(p => p.Content)
                //    //        .ToList();
                //    //    string msg = "您的帐号注册信息审核被拒绝";
                //    //    if (userAuditRecords.Count > 0)
                //    //    {
                //    //        msg += "[";
                //    //        foreach (var item in userAuditRecords)
                //    //        {
                //    //            msg += userAuditRecords.IndexOf(item) + 1 + ":" + item + "、";
                //    //        }
                //    //        msg = msg.Substring(0, msg.Length - 1);
                //    //        msg += "]";
                //    //    }
                //    //    return new SubmitLoginOutput()
                //    //    {
                //    //        IsLogin = false,
                //    //        Msg = msg
                //    //    };
                //    //}

                //    //生成token
                //    JWTPayload jWTPayload = new JWTPayload
                //    {
                //        UserId = studentInfoId.RassociationId,
                //        UserName = student.UserName,
                //        //RealName = student.RealName,
                //        Platform = input.Source,
                //        Expire = DateTime.Parse(DateTime.Now.AddDays(2).ToString("yyyy-MM-dd") + " 03:00:00")
                //    };
                //    string token = UwooJwtHelper.GetToken(jWTPayload.ToJsonString());
                //    Logger.Add(LoggerType.Login.ToString(), input.Serialize(), "登陆成功", null, LoggerStatus.Info, student.Id, input.Source.ToString());
                //    Task.Run(() =>
                //    {
                //        DBSqlSugar.Insertable(new Sys_UserLoginLogs
                //        {
                //            UserId = student.Id,
                //            UserType = 0,
                //            CreateTime = DateTime.Now,
                //            Platform = input.Source.ToString(),
                //            LoginDate = DateTime.Now.ToString("yyyy-MM-dd"),
                //            RequestAgent = input.RequestAgent
                //        }).ExecuteCommand();
                //    });
                //    return new SubmitLoginOutput()
                //    {
                //        IsLogin = true,
                //        Msg = "登录成功!",
                //        Token = token
                //    };
                //}
                //else if (input.Type == 1)
                //{
                //    //获取教师信息
                //    Base_User user = DBSqlSugar.Queryable<Base_User>().Where(x => x.UserName == input.PhoneNo && x.Deleted == false).First();
                //    if (user == null)
                //    {
                //        user = DBSqlSugar.Queryable<Base_User>().Where(x => x.PhoneNo == input.PhoneNo && x.Deleted == false).First();
                //        if (user == null)
                //        {
                //            return new SubmitLoginOutput()
                //            {
                //                IsLogin = false,
                //                Msg = "账号未注册!"
                //            };
                //        }
                //    }
                //    //验证密码
                //    if (input.Password.Trim().EncryptDES(AppSetting.Secret.User) != (user.PassWord ?? ""))
                //    {
                //        return new SubmitLoginOutput()
                //        {
                //            IsLogin = false,
                //            Msg = "密码不正确!"
                //        };
                //    }
                //    //获取教师信息
                //    Sys_UserAssociation_Mapping teacherInfoId = DBSqlSugar.Queryable<Sys_UserAssociation_Mapping>().Where(p => p.UserId == user.Id && p.UserType != 1 && p.Deleted == false).First();
                //    if (teacherInfoId == null)
                //    {
                //        return new SubmitLoginOutput()
                //        {
                //            IsLogin = false,
                //            Msg = "当前用户未绑定教师信息，请联系管理员!"
                //        };
                //    }
                //    ////验证账号状态
                //    //if (user.AuditStatus == 1)
                //    //{
                //    //    return new SubmitLoginOutput()
                //    //    {
                //    //        IsLogin = false,
                //    //        Msg = "您的帐号注册信息在审核中，待审核通过即可登录！"
                //    //    };
                //    //}
                //    //if (user.AuditStatus == 3)
                //    //{
                //    //    //获取审核记录
                //    //    List<string> userAuditRecords = DBSqlSugar.Queryable<Sys_UserAuditRecord>()
                //    //        .Where(p => p.UserId == user.Id && p.UserType == 2 && p.AuditState == 2)
                //    //        .OrderByDescending(p => p.CreateTime).Select(p => p.Content)
                //    //        .ToList();
                //    //    string msg = "您的帐号注册信息审核被拒绝";
                //    //    if (userAuditRecords.Count > 0)
                //    //    {
                //    //        msg += "[";
                //    //        foreach (var item in userAuditRecords)
                //    //        {
                //    //            msg += userAuditRecords.IndexOf(item) + 1 + "：" + item + "、";
                //    //        }
                //    //        msg = msg.Substring(0, msg.Length - 1);
                //    //        msg += "]";
                //    //    }
                //    //    return new SubmitLoginOutput()
                //    //    {
                //    //        IsLogin = false,
                //    //        Msg = msg
                //    //    };
                //    //}

                //    //生成token
                //    JWTPayload jWTPayload = new JWTPayload
                //    {
                //        UserId = teacherInfoId.RassociationId,
                //        UserName = user.UserName,
                //        //RealName = user.RealName,
                //        Platform = input.Source,
                //        Expire = DateTime.Parse(DateTime.Now.AddDays(2).ToString("yyyy-MM-dd") + " 03:00:00")
                //    };
                //    string token = UwooJwtHelper.GetToken(jWTPayload.ToJsonString());
                //    //string token = JwtHelper.IssueJwt(jWTPayload);

                //    Logger.Add(LoggerType.Login.ToString(), input.Serialize(), "登陆成功", null, LoggerStatus.Info, user.Id, input.Source.ToString());
                //    Task.Run(() =>
                //    {
                //        DBSqlSugar.Insertable(new Sys_UserLoginLogs
                //        {
                //            UserId = user.Id,
                //            UserType = 1,
                //            CreateTime = DateTime.Now,
                //            Platform = input.Source.ToString(),
                //            LoginDate = DateTime.Now.ToString("yyyy-MM-dd"),
                //            RequestAgent = input.RequestAgent
                //        }).ExecuteCommand();
                //    });
                //    return new SubmitLoginOutput()
                //    {
                //        IsLogin = true,
                //        Msg = "登录成功!",
                //        Token = token
                //    };
                //}
                //else
                //{
                //    return new SubmitLoginOutput()
                //    {
                //        IsLogin = false,
                //        Msg = "登录类型错误!",
                //    };
                //}
            }
            catch (Exception ex)
            {
                return new SubmitLoginOutput()
                {
                    IsLogin = false,
                    Msg = ex.Message
                };
            }
        }
    }
}
