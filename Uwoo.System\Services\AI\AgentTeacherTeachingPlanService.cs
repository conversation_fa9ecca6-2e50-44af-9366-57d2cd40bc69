﻿using AutoMapper;
using Coldairarrow.Util;
using MongoDB.Bson;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharpCompress.Common;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using ThirdParty.Json.LitJson;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Core.CacheManager.BusinessCacheService;
using UwooAgent.Core.CacheManager.IBusinessCacheService;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Entity.DomainModels.Exam;
using UwooAgent.Entity.DomainModels.Office;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;
using UwooAgent.Model.Word;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;
using static cn.jpush.api.report.UsersResult;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_教师端教案
    /// </summary>
    public class AgentTeacherTeachingPlanService : ServiceBase<AI_TeachingPlan, IAgentTeacherTeachingPlanRepository>, IAgentTeacherTeachingPlanService, IDependency
    {
        #region DI

        private IBase_SemesterTimeService _semesterTimeService;
        private IAgentCommonService _agentCommonService;
        private IOfficeFileCacheService _officeFileCacheService;
        public AgentTeacherTeachingPlanService(IBase_SemesterTimeService semesterTimeService,
            IAgentCommonService agentCommonService,
            IOfficeFileCacheService officeFileCacheService)
        {
            _semesterTimeService = semesterTimeService;
            _agentCommonService = agentCommonService;
            _officeFileCacheService = officeFileCacheService;
        }

        #endregion

        /// <summary>
        /// 保存教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task SaveTeachingPlanContentDemand(SaveTeachingPlanContentDemandInput input)
        {
            try
            {
                AI_TeachingPlanContentDemand contentDemand = new AI_TeachingPlanContentDemand()
                {
                    Id = IdHelper.GetId(),
                    Title = input.Title,
                    Describe = input.Describe,
                    Type = 2,
                    CreateTime = DateTime.Now,
                    Creator = input.TeacherId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.TeacherId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(contentDemand).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 删除教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task DelTeachingPlanContentDemand(DelTeachingPlanContentDemandInput input)
        {
            try
            {
                //删除教案内容要求
                await DBSqlSugar.Updateable<AI_TeachingPlanContentDemand>()
                    .SetColumns(it => new AI_TeachingPlanContentDemand() { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.Id == input.Id && p.Creator == input.TeacherId && p.Type == 2)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取教案内容要求
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<GetTeachingPlanContentDemandOutput>> GetTeachingPlanContentDemand(GetTeachingPlanContentDemandInput input)
        {
            try
            {
                string sql = @"SELECT
                                	* 
                                FROM
                                	AI_TeachingPlanContentDemand WITH ( NOLOCK )
                                WHERE
                                	IsDeleted = 0 
                                	AND ( type = 1 OR ( Type = 2 AND Creator = @teacherId ) )
                                ORDER BY
                                	Type,
                                	CreateTime";
                List<GetTeachingPlanContentDemandOutput> outputs = await DBSqlSugar.SqlQueryable<GetTeachingPlanContentDemandOutput>(sql)
                    .AddParameters(new { teacherId = input.TeacherId }).ToListAsync();
                return outputs;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 创建教案
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task CreateTeachingPlan(CreateTeachingPlanInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).With(SqlWith.NoLock).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!");
                }

                //获取模型Id
                AI_ModelBaseInfo modelBaseInfo = await DBSqlSugar.Queryable<AI_ModelBaseInfo>().Where(p => p.Id == input.ModelId && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (modelBaseInfo == null || string.IsNullOrEmpty(modelBaseInfo.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取学科
                Exam_Subject subjectInfo = await DBSqlSugar.Queryable<Exam_Subject>().Where(p => p.Id == input.SubjectId).With(SqlWith.NoLock).FirstAsync();

                //获取内容要求
                List<AI_TeachingPlanContentDemand> contentDemandInfos = await DBSqlSugar.Queryable<AI_TeachingPlanContentDemand>().Where(p => input.ContentDemandId.Contains(p.Id) && p.IsDeleted == false).With(SqlWith.NoLock).ToListAsync();
                string contentDemandDataText = string.Empty;
                foreach (var contentDemandInfo in contentDemandInfos)
                {
                    if (string.IsNullOrEmpty(contentDemandInfo.Describe))
                    {
                        contentDemandInfo.Describe = "暂无";
                    }
                    contentDemandDataText += $"标题:{contentDemandInfo.Title}、描述:{contentDemandInfo.Describe}。\n";
                }

                //创建教案提示词
                string msg = string.Empty;
                if (input.Type == 1)
                {
                    //获取标题创建教案指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherTitleCreateTeachingPlan_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取标题创建教案提示词,请联系管理员!");
                    }

                    msg = directive.Directive.Replace("[年级]", BusinessUtil.GradeName(input.Grade))
                        .Replace("[学科]", subjectInfo.Name)
                        .Replace("[标题]", input.Title)
                        .Replace("[内容要求]", contentDemandDataText)
                        .Replace("[其他要求]", string.IsNullOrEmpty(input.Demand) ? "暂无" : input.Demand);
                }
                else if (input.Type == 2)
                {
                    //获取文本创建教案指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherTextCreateTeachingPlan_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取文本创建教案提示词,请联系管理员!");
                    }

                    // 精准匹配HTTP/HTTPS URL（包含所有合法字符）
                    string pattern = @"https?://[a-zA-Z0-9\-._~:/?#\[\]@!$&'()*+,;=]+";
                    Regex regex = new Regex(pattern);
                    MatchCollection matches = regex.Matches(input.Content);
                    List<string> imgUrl = new List<string>();
                    foreach (Match match in matches)
                    {
                        imgUrl.Add(match.Value);
                    }
                    string content = input.Content;
                    if (imgUrl.Count > 0)
                    {
                        //文件解析
                        List<FileAnalysisDouBaoOutput> fileAnalyses = await _agentCommonService.FileAnalysis(new FileAnalysisInput() { Msg = new List<string>() { input.Content } });
                        content = fileAnalyses.Count > 0 ? string.Join('。', fileAnalyses.Select(p => p.Content)) : content;
                    }

                    msg = directive.Directive.Replace("[年级]", BusinessUtil.GradeName(input.Grade))
                        .Replace("[学科]", subjectInfo.Name)
                        .Replace("[内容要求]", contentDemandDataText)
                        .Replace("[教案内容]", content);
                }
                else if (input.Type == 3)
                {
                    //获取章节创建教案指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherChapterCreateTeachingPlan_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取章节创建教案提示词,请联系管理员!");
                    }

                    //获取章节名称
                    string chapterName = await DBSqlSugar.Queryable<Exam_SubjectChapter>().Where(p => p.Id == input.ChapterId).Select(p => p.ChapterName).With(SqlWith.NoLock).FirstAsync();

                    msg = directive.Directive.Replace("[年级]", BusinessUtil.GradeName(input.Grade))
                        .Replace("[学科]", subjectInfo.Name)
                        .Replace("[章节]", chapterName)
                        .Replace("[内容要求]", contentDemandDataText)
                        .Replace("[其他要求]", string.IsNullOrEmpty(input.Demand) ? "暂无" : input.Demand);
                }
                else if (input.Type == 4)
                {
                    //获取文档创建教案指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherDocumentCreateTeachingPlan_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取文档创建教案提示词,请联系管理员!");
                    }

                    //文件解析
                    List<FileAnalysisDouBaoOutput> fileAnalyses = await _agentCommonService.FileAnalysis(new FileAnalysisInput() { Msg = new List<string>() { input.FileUrl } });

                    msg = directive.Directive.Replace("[年级]", BusinessUtil.GradeName(input.Grade))
                        .Replace("[学科]", subjectInfo.Name)
                        .Replace("[内容要求]", contentDemandDataText)
                        .Replace("[文档解析]", fileAnalyses.Count > 0 ? string.Join('。', fileAnalyses.Select(p => p.Content)) : "暂无")
                        .Replace("[其他要求]", string.IsNullOrEmpty(input.Demand) ? "暂无" : input.Demand);
                }

                //对话
                string resData = string.Empty;
                await _agentCommonService.AIDialogue(new AIDialogueInput()
                {
                    ModelKey = modelBaseInfo.Modelkey,
                    Msg = msg,
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);

                    string json = data;
                    int startIndex = json.IndexOf('{');
                    json = json.Substring(startIndex);
                    JObject jsonObject = JObject.Parse(json);
                    string content = jsonObject["Content"]?.Value<string>();
                    if (content != null)
                    {
                        resData += content;
                    }
                }, cancellationToken);

                //截取教案名称（长度限制在100以内）
                //分割出第一行（处理 Windows 和 Linux 换行符）
                string[] lines = resData.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None);
                string firstLine = lines.Length > 0 ? lines[0] : string.Empty;
                string name = firstLine.Length > 100 ? firstLine.Substring(0, 100) : firstLine;

                //保存教案生成记录
                AI_TeachingPlanCreateRecord teachingPlanCreateRecord = new AI_TeachingPlanCreateRecord()
                {
                    Id = IdHelper.GetId(),
                    Name = name,
                    Type = input.Type,
                    ModelId = input.ModelId,
                    Grade = input.Grade,
                    SubjectId = input.SubjectId,
                    ChapterId = input.ChapterId,
                    Content = input.Content,
                    Demand = input.Demand,
                    FileUrl = input.FileUrl,
                    FileName = input.FileName,
                    Title = input.Title,
                    Year = nowSemesterTime.Year,
                    Term = nowSemesterTime.NowTerm,
                    TeachingPlanText = resData,
                    CreateTime = DateTime.Now,
                    Creator = input.TeacherId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.TeacherId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(teachingPlanCreateRecord).ExecuteCommandAsync();
                List<AI_TeachingPlanContentDemandMapping> teachingPlanContentDemandMappings = new List<AI_TeachingPlanContentDemandMapping>();
                foreach (var item in input.ContentDemandId)
                {
                    teachingPlanContentDemandMappings.Add(new AI_TeachingPlanContentDemandMapping()
                    {
                        Id = IdHelper.GetId(),
                        TeachingPlanContentDemandId = item,
                        TeachingPlanCreateRecordId = teachingPlanCreateRecord.Id,
                        CreateTime = DateTime.Now,
                        Creator = input.TeacherId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false
                    });
                }
                await DBSqlSugar.Insertable(teachingPlanContentDemandMappings).ExecuteCommandAsync();

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    BusinessId = teachingPlanCreateRecord.Id,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 修改教案创建记录中的教案文本
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task UpdateTeachingPlanCreateRecord(UpdateTeachingPlanCreateRecordInput input)
        {
            try
            {
                await DBSqlSugar.Updateable<AI_TeachingPlanCreateRecord>()
                    .SetColumns(it => new AI_TeachingPlanCreateRecord() { TeachingPlanText = input.TeachingPlanText, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.Id == input.Id && p.Creator == input.TeacherId)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取教案创建记录列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<PageReturn<GetTeachingPlanCreateRecordOutput>> GetTeachingPlanCreateRecord(GetTeachingPlanCreateRecordInput input)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).With(SqlWith.NoLock).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!");
                }

                string sql = @"SELECT
                                	Id,
                                	Name,
                                	TeachingPlanText,
                                	CreateTime 
                                FROM
                                	[dbo].[AI_TeachingPlanCreateRecord] WITH ( NOLOCK )
                                WHERE
                                	IsDeleted = 0 
                                	AND Creator = @teacherId 
                                    AND SubjectId =@subjectId
                                    AND Grade=@grade
                                	AND [Year] = @year 
                                	AND Term =@term";
                RefAsync<int> totalNumber = 0;
                List<GetTeachingPlanCreateRecordOutput> agentStudentHomePages = await DBSqlSugar.SqlQueryable<GetTeachingPlanCreateRecordOutput>(sql)
                    .AddParameters(new
                    {
                        teacherId = input.TeacherId,
                        subjectId = input.SubjectId,
                        grade = input.Grade,
                        year = nowSemesterTime.Year,
                        term = nowSemesterTime.NowTerm
                    })
                    .OrderBy("CreateTime DESC")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                return new PageReturn<GetTeachingPlanCreateRecordOutput>()
                {
                    Datas = agentStudentHomePages,
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 教案创建记录文本润色
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        public async Task TeachingPlanCreateRecordOptimize(TeachingPlanCreateRecordOptimizeInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取教案创建记录
                AI_TeachingPlanCreateRecord teachingPlanCreateRecord = await DBSqlSugar.Queryable<AI_TeachingPlanCreateRecord>().Where(p => p.Id == input.Id && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (teachingPlanCreateRecord == null)
                {
                    throw new BusException("教案创建记录Id异常!");
                }

                //获取模型Id
                AI_ModelBaseInfo modelBaseInfo = await DBSqlSugar.Queryable<AI_ModelBaseInfo>().Where(p => p.Id == teachingPlanCreateRecord.ModelId && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (modelBaseInfo == null || string.IsNullOrEmpty(modelBaseInfo.Modelkey))
                {
                    throw new BusException("智能体配置的模型异常!");
                }

                //获取教案创建记录润色key
                string key = AIAgentKeys.GetTeachingPlanCreateRecordOptimize(input.Id, input.TeacherId);
                if (string.IsNullOrEmpty(key))
                {
                    throw new BusException("获取教案创建记录润色key异常!");
                }

                //获取豆包上下文缓存Id
                bool isCreate = false;
                AI_ContextCacheKey contextCacheKey = await DBSqlSugar.Queryable<AI_ContextCacheKey>().Where(p => p.CacheKey == key && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                if (contextCacheKey != null)
                {
                    //验证是否过期
                    if (contextCacheKey.ExpirationTime <= DateTime.Now)
                    {
                        contextCacheKey.IsDeleted = true;
                        await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        isCreate = true;
                    }
                }
                else
                {
                    isCreate = true;
                }

                if (isCreate)
                {
                    //获取指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeachingPlanCreateRecordOptimize_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取教案润色提示词,请联系管理员!");
                    }

                    //获取学科
                    Exam_Subject subjectInfo = await DBSqlSugar.Queryable<Exam_Subject>().Where(p => p.Id == teachingPlanCreateRecord.SubjectId).With(SqlWith.NoLock).FirstAsync();

                    //年级名称
                    string gradeName = BusinessUtil.GradeName(teachingPlanCreateRecord.Grade);

                    string contextMsg = directive.Directive.Replace("[年级]", gradeName).Replace("[学科]", subjectInfo?.Name).Replace("[教案内容]", teachingPlanCreateRecord.TeachingPlanText);

                    //创建上下文缓存
                    string contextId = await _agentCommonService.CreateContext(new CreateContextInput()
                    {
                        Msg = contextMsg,
                        TimeOut = 604800,
                        modelId = modelBaseInfo.Modelkey
                    });
                    if (string.IsNullOrEmpty(contextId))
                    {
                        throw new Exception("创建上下文缓存异常!");
                    }

                    contextCacheKey = new AI_ContextCacheKey()
                    {
                        Id = IdHelper.GetId(),
                        CacheKey = key,
                        CacheId = contextId,
                        CreateTime = DateTime.Now,
                        TimeOut = 604800,
                        Explain = "教案创建记录润色key",
                        ExpirationTime = DateTime.Now.AddSeconds(604800),
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(contextCacheKey).ExecuteCommandAsync();
                }

                //上下文对话
                string msg = $"1.优化类型:{input.Scheme}。\n2.需要优化的文本:{input.OptimizeText}。\n";
                if (!string.IsNullOrEmpty(input.Prompt))
                {
                    msg += $"3.优化要求:{input.Prompt}。";
                }
                await _agentCommonService.ContextDialogue(new ContextDialogueInput()
                {
                    context_id = contextCacheKey.CacheId,
                    content = msg,
                    role = "user",
                    modelId = modelBaseInfo.Modelkey
                }, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await dataHandler(data);
                }, cancellationToken);

                //更新过期时间
                contextCacheKey.ExpirationTime = DateTime.Now.AddSeconds(604800);
                await DBSqlSugar.Updateable(contextCacheKey).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                //执行结束
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 教案创建记录详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<TeachingPlanCreateRecordDetailsOutput> TeachingPlanCreateRecordDetails(TeachingPlanCreateRecordDetailsInput input)
        {
            try
            {
                //获取教案创建记录
                TeachingPlanCreateRecordDetailsOutput teachingPlanCreateRecord = await DBSqlSugar.Queryable<AI_TeachingPlanCreateRecord>().Where(p => p.Id == input.Id && p.IsDeleted == false)
                    .Select(p => new TeachingPlanCreateRecordDetailsOutput()
                    {
                        Id = p.Id,
                        ChapterId = p.ChapterId,
                        Content = p.Content,
                        Demand = p.Demand,
                        FileName = p.FileName,
                        FileUrl = p.FileUrl,
                        Grade = p.Grade,
                        ModelId = p.ModelId,
                        Name = p.Name,
                        SubjectId = p.SubjectId,
                        TeachingPlanText = p.TeachingPlanText,
                        Title = p.Title,
                        Type = p.Type
                    })
                    .With(SqlWith.NoLock).FirstAsync();
                if (teachingPlanCreateRecord != null)
                {
                    //获取内容要求
                    List<string> contentDemandIds = await DBSqlSugar.Queryable<AI_TeachingPlanContentDemandMapping>()
                        .Where(p => p.TeachingPlanCreateRecordId == teachingPlanCreateRecord.Id && p.IsDeleted == false)
                        .Select(p => p.TeachingPlanContentDemandId)
                        .With(SqlWith.NoLock).ToListAsync();
                    teachingPlanCreateRecord.ContentDemandId = contentDemandIds;
                }
                return teachingPlanCreateRecord;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 教案创建记录文本转word
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<TeachingPlanCreateRecordTextToWordOutput> TeachingPlanCreateRecordTextToWord(TeachingPlanCreateRecordTextToWordInput input)
        {
            try
            {
                //获取教案创建记录文本
                AI_TeachingPlanCreateRecord teachingPlanCreateRecord = await DBSqlSugar.Queryable<AI_TeachingPlanCreateRecord>()
                    .Where(p => p.Id == input.Id && p.IsDeleted == false)
                    .With(SqlWith.NoLock).FirstAsync();
                if (teachingPlanCreateRecord == null || string.IsNullOrEmpty(teachingPlanCreateRecord.TeachingPlanText))
                {
                    throw new BusException("无法获取教案文本!");
                }

                //地址
                string url = AppSetting.OfficeTool.OfficeToolUrl + AppSetting.OfficeTool.MarkdownTextToWordUrl;

                //参数
                MarkdownTextToWordInput markdownTextToWordInput = new MarkdownTextToWordInput()
                {
                    Text = teachingPlanCreateRecord.TeachingPlanText,
                    BucketName = "aidialoguefileu"
                };
                string jsonData = JsonConvert.SerializeObject(markdownTextToWordInput);

                string fileUrl = string.Empty;
                //http
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(120);
                    var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                    HttpResponseMessage response = await httpClient.PostAsync(url, content);
                    if (response.IsSuccessStatusCode)
                    {
                        fileUrl = await response.Content.ReadAsStringAsync();
                    }
                    else
                    {
                        throw new BusException("生成失败!");
                    }
                }

                //获取当前教案生成记录文件数量
                int fileCount = await DBSqlSugar.Queryable<AI_TeachingPlanCreateRecordFile>().Where(p => p.TeachingPlanCreateRecordId == teachingPlanCreateRecord.Id && p.IsDeleted == false).CountAsync();

                // 定义允许的字符模式：字母、数字、中文和下划线
                string pattern = @"[^a-zA-Z0-9\u4e00-\u9fa5_]";
                // 使用正则表达式替换所有特殊符号为空字符串
                string fileName = Regex.Replace(teachingPlanCreateRecord.Name, pattern, "");
                if (fileCount > 0)
                {
                    fileName = fileName + $"({fileCount}).docx";
                }
                else
                {
                    fileName = fileName + ".docx";
                }
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = Guid.NewGuid().ToString("N") + ".docx";
                }
                AI_TeachingPlanCreateRecordFile teachingPlanCreateRecordFile = new AI_TeachingPlanCreateRecordFile()
                {
                    Id = IdHelper.GetId(),
                    FileName = fileName,
                    FileUrl = fileUrl,
                    FileType = 1,
                    TeachingPlanCreateRecordId = teachingPlanCreateRecord.Id,
                    CreateTime = DateTime.Now,
                    Creator = input.TeacherId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.TeacherId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(teachingPlanCreateRecordFile).ExecuteCommandAsync();

                return new TeachingPlanCreateRecordTextToWordOutput()
                {
                    Id = teachingPlanCreateRecordFile.Id,
                    FileName = teachingPlanCreateRecordFile.FileName,
                    FileUrl = teachingPlanCreateRecordFile.FileUrl,
                    TeachingPlanCreateRecordId = teachingPlanCreateRecordFile.TeachingPlanCreateRecordId
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 教案生成记录修改文件名称
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task TeachingPlanCreateRecordFileUpdateName(TeachingPlanCreateRecordFileUpdateNameInput input)
        {
            try
            {
                // 1. 获取文件记录
                AI_TeachingPlanCreateRecordFile file = await DBSqlSugar.Queryable<AI_TeachingPlanCreateRecordFile>().Where(p => p.Id == input.Id && p.Creator == input.TeacherId && p.IsDeleted == false).FirstAsync();
                if (file == null)
                {
                    throw new BusException("无法获取文件信息");
                }

                // 2. 提取原文件后缀
                string extension = Path.GetExtension(file.FileName);
                if (string.IsNullOrWhiteSpace(extension))
                {
                    throw new BusException("原文件无有效后缀，无法处理");
                }

                // 3. 提取用户输入的主名（去掉所有后缀，如"a.txt"→"a"）
                string rawMainName = Path.GetFileNameWithoutExtension(input.FileName);

                // 4. 过滤主名中的非法字符（仅保留字母、数字、中文、下划线）
                string pattern = @"[^a-zA-Z0-9\u4e00-\u9fa5_]";
                string filteredMainName = Regex.Replace(rawMainName, pattern, "");

                // 5. 校验主名非空（避免过滤后为空，导致文件名仅含后缀）
                if (string.IsNullOrWhiteSpace(filteredMainName))
                {
                    throw new BusException("文件名不能为空（过滤后主名无效）");
                }

                // 6. 拼接新文件名（主名 + 原后缀）
                string newFileName = filteredMainName + extension;

                // 7. 更新文件记录
                file.FileName = newFileName;
                file.Modifier = input.TeacherId;
                file.ModifyTime = DateTime.Now;

                await DBSqlSugar.Updateable(file).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取教案生成记录文件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<TeachingPlanCreateRecordFileOutput>> TeachingPlanCreateRecordFile(TeachingPlanCreateRecordFileInput input)
        {
            try
            {
                List<TeachingPlanCreateRecordFileOutput> fileOutputs = await DBSqlSugar.Queryable<AI_TeachingPlanCreateRecordFile>()
                    .Where(p => p.TeachingPlanCreateRecordId == input.Id && p.Creator == input.TeacherId && p.IsDeleted == false)
                    .Select(p => new TeachingPlanCreateRecordFileOutput()
                    {
                        Id = p.Id,
                        TeachingPlanCreateRecordId = p.TeachingPlanCreateRecordId,
                        FileName = p.FileName,
                        FileUrl = p.FileUrl,
                        FileType = p.FileType
                    }).ToListAsync();
                return fileOutputs;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 保存教案
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task SaveTeachingPlan(SaveTeachingPlanInput input)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).With(SqlWith.NoLock).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                //教案信息
                AI_TeachingPlan teachingPlan = new AI_TeachingPlan()
                {
                    Id = IdHelper.GetId(),
                    Name = input.Name,
                    Type = input.Type,
                    Content = input.Text,
                    SubjectId = input.SubjectId,
                    GradeId = input.GradeId,
                    Year = nowSemesterTime.Year,
                    Term = nowSemesterTime.NowTerm,
                    CreateTime = DateTime.Now,
                    Creator = input.TeacherId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.TeacherId,
                    IsDeleted = false
                };
                await DBSqlSugar.Insertable(teachingPlan).ExecuteCommandAsync();

                if (teachingPlan.Type == 2)
                {
                    //Word草稿本记录
                    Exam_DZBWordImportHistory wordImportHistory = new Exam_DZBWordImportHistory()
                    {
                        Id = IdHelper.GetId(),
                        FileName = input.Name,
                        Url = input.FileUrl,
                        CreateTime = DateTime.Now,
                        CreatorId = input.TeacherId
                    };
                    await DBSqlSugar.Insertable(wordImportHistory).ExecuteCommandAsync();

                    //office文件
                    //获取最新版本号
                    var version = _officeFileCacheService.GetOfficeVersion(wordImportHistory.Id);
                    Office_File office = new Office_File()
                    {
                        Id = IdHelper.GetLongId(),
                        FileId = wordImportHistory.Id,
                        FileName = wordImportHistory.FileName,
                        Url = wordImportHistory.Url,
                        Size = 100,
                        Version = (int)version,
                        CreatorId = input.TeacherId,
                        CreateTime = DateTime.Now
                    };
                    await DBSqlSugar.Insertable(office).ExecuteCommandAsync();

                    //AI_教案和点阵笔试卷Word草稿本记录关联
                    AI_TeachingPlanDZBWordImportHistoryMapping teachingPlanDZBWordImportHistoryMapping = new AI_TeachingPlanDZBWordImportHistoryMapping()
                    {
                        Id = IdHelper.GetId(),
                        TeachingPlanId = teachingPlan.Id,
                        DZBWordImportHistoryId = wordImportHistory.Id,
                        CreateTime = DateTime.Now,
                        Creator = input.TeacherId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false
                    };
                    await DBSqlSugar.Insertable(teachingPlanDZBWordImportHistoryMapping).ExecuteCommandAsync();
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取教案列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageReturn<GetTeachingPlanOutput>> GetTeachingPlan(GetTeachingPlanInput input)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).With(SqlWith.NoLock).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                string sql = @"SELECT
                                	tp.Id,
                                	tp.Name,
                                	tp.Createtime,
                                    tp.ModifyTime,
                                	tp.Type,
                                	tpwh.DZBWordImportHistoryId AS FileId 
                                FROM
                                	AI_TeachingPlan tp WITH ( NOLOCK )
                                	LEFT JOIN AI_TeachingPlanDZBWordImportHistoryMapping tpwh WITH ( NOLOCK ) ON tp.Id= tpwh.TeachingPlanId 
                                WHERE
                                	tp.IsDeleted= 0 
                                	AND tp.Creator= @teacherId 
                                	AND tp.SubjectId= @subjectId
                                	AND tp.GradeId= @gradeId
                                	AND tp.[Year] = @year 
                                	AND tp.Term=@term";
                RefAsync<int> totalNumber = 0;
                List<GetTeachingPlanOutput> teachingPlanOutputs = await DBSqlSugar.SqlQueryable<GetTeachingPlanOutput>(sql)
                    .AddParameters(new
                    {
                        teacherId = input.TeacherId,
                        subjectId = input.SubjectId,
                        gradeId = input.GradeId,
                        year = nowSemesterTime.Year,
                        term = nowSemesterTime.NowTerm
                    })
                    .OrderBy("ModifyTime DESC")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                return new PageReturn<GetTeachingPlanOutput>()
                {
                    Datas = teachingPlanOutputs,
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取教案详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetTeachingPlanDetailsOutput> GetTeachingPlanDetails(GetTeachingPlanDetailsInput input)
        {
            try
            {
                GetTeachingPlanDetailsOutput detailsOutput = await DBSqlSugar.Queryable<AI_TeachingPlan>()
                    .Where(p => p.Id == input.Id && p.IsDeleted == false)
                    .Select(p => new GetTeachingPlanDetailsOutput()
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Text = p.Content,
                        Type = p.Type
                    }).FirstAsync();
                return detailsOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 教案文本类型编辑
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task TeachingPlanTextUpdate(TeachingPlanTextUpdateInput input)
        {
            try
            {
                await DBSqlSugar.Updateable<AI_TeachingPlan>()
                    .SetColumns(it => new AI_TeachingPlan() { Name = input.Name, Content = input.Text, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.Id == input.Id && p.Creator == input.TeacherId && p.Type == 1)
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 教案重命名
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task TeachingPlanUpdateName(TeachingPlanUpdateNameInput input)
        {
            try
            {
                //获取教案信息
                AI_TeachingPlan teachingPlan = await DBSqlSugar.Queryable<AI_TeachingPlan>().Where(p => p.Id == input.Id && p.Creator == input.TeacherId && p.IsDeleted == false).FirstAsync();
                if (teachingPlan == null)
                {
                    throw new BusException("教案Id异常!");
                }
                if (teachingPlan.Type == 2)
                {
                    //提取原文件后缀
                    string extension = Path.GetExtension(teachingPlan.Name);
                    if (string.IsNullOrWhiteSpace(extension))
                    {
                        throw new BusException("原文件无有效后缀，无法处理");
                    }

                    //提取用户输入的主名（去掉所有后缀，如"a.txt"→"a"）
                    string rawMainName = Path.GetFileNameWithoutExtension(input.Name);

                    //过滤主名中的非法字符（仅保留字母、数字、中文、下划线）
                    string pattern = @"[^a-zA-Z0-9\u4e00-\u9fa5_]";
                    string filteredMainName = Regex.Replace(rawMainName, pattern, "");

                    //校验主名非空（避免过滤后为空，导致文件名仅含后缀）
                    if (string.IsNullOrWhiteSpace(filteredMainName))
                    {
                        throw new BusException("文件名不能为空（过滤后主名无效）");
                    }

                    //拼接新文件名（主名 + 原后缀）
                    string newFileName = filteredMainName + extension;

                    teachingPlan.Name = newFileName;
                }
                else
                {
                    teachingPlan.Name = input.Name;
                }
                teachingPlan.ModifyTime = DateTime.Now;
                teachingPlan.Modifier = input.TeacherId;
                await DBSqlSugar.Updateable(teachingPlan).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 删除教案
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task DelTeachingPlan(DelTeachingPlanInput input)
        {
            try
            {
                await DBSqlSugar.Updateable<AI_TeachingPlan>()
                   .SetColumns(it => new AI_TeachingPlan() { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                   .Where(p => p.Id == input.Id && p.Creator == input.TeacherId)
                   .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
