using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.UserManager;
using Uwoo.Entity.DomainModels;
using Uwoo.Model.CustomException;
using Uwoo.System.Services;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Entity.DomainModels.Exam;
using UwooAgent.Entity.DomainModels.Question;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_智能出题服务实现
    /// </summary>
    public class AgentIntelligentQuestionService : ServiceBase<AI_AgentBaseInfo, IAgentIntelligentQuestionRepository>, IAgentIntelligentQuestionService, IDependency
    {
        #region DI
        private readonly IAgentCommonService _agentCommonService;
        private readonly Base_SemesterTimeService _semesterTimeService;
        public AgentIntelligentQuestionService(IAgentCommonService agentCommonService,Base_SemesterTimeService semesterTimeService)
        {
            _agentCommonService = agentCommonService;
            _semesterTimeService= semesterTimeService;
        }
        #endregion

        /// <summary>
        /// 获取题型列表
        /// </summary>
        /// <returns></returns>
        public List<QuestionTypeDto> GetQuestionTypes()
        {
            return new List<QuestionTypeDto>
            {
                new QuestionTypeDto { Id = 1, Name = "单项选择题", Description = "从多个选项中选择一个正确答案" },
                new QuestionTypeDto { Id = 2, Name = "多项选择题", Description = "从多个选项中选择多个正确答案" },
                new QuestionTypeDto { Id = 3, Name = "判断题", Description = "判断题目描述是否正确" },
                new QuestionTypeDto { Id = 4, Name = "填空题", Description = "在空白处填入正确答案" },
                new QuestionTypeDto { Id = 5, Name = "主观题", Description = "需要详细阐述或分析的题目" }
            };
        }

        /// <summary>
        /// 获取难度等级列表
        /// </summary>
        /// <returns></returns>
        public List<DifficultyLevelDto> GetDifficultyLevels()
        {
            return new List<DifficultyLevelDto>
            {
                new DifficultyLevelDto { Id = 1, Name = "容易", Description = "基础知识点，难度较低" },
                new DifficultyLevelDto { Id = 2, Name = "较易", Description = "简单应用，稍有难度" },
                new DifficultyLevelDto { Id = 3, Name = "中等", Description = "综合应用，中等难度" },
                new DifficultyLevelDto { Id = 4, Name = "较难", Description = "深入理解，难度较高" },
                new DifficultyLevelDto { Id = 5, Name = "困难", Description = "综合分析，难度很高" }
            };
        }

        /// <summary>
        /// 获取出题方向列表
        /// </summary>
        /// <returns></returns>
        public List<QuestionDirectionDto> GetQuestionDirections()
        {
            return new List<QuestionDirectionDto>
            {
                new QuestionDirectionDto { Id = 1, Name = "识记", Description = "考查基础知识的记忆和识别" },
                new QuestionDirectionDto { Id = 2, Name = "理解", Description = "考查对知识的理解和解释" },
                new QuestionDirectionDto { Id = 3, Name = "掌握", Description = "考查知识的熟练运用" },
                new QuestionDirectionDto { Id = 4, Name = "应用", Description = "考查知识在实际情境中的应用" },
                new QuestionDirectionDto { Id = 5, Name = "综合", Description = "考查知识的综合运用和创新" }
            };
        }

        /// <summary>
        /// 根据学科获取知识点列表（树形结构）
        /// </summary>
        /// <param name="subjectId">学科ID</param>
        /// <returns></returns>
        public async Task<List<KnowledgePointDto>> GetKnowledgePointsBySubject(string subjectId)
        {
            //获取当前学年学期
            NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
            if (nowSemesterTime == null)
            {
                throw new BusException("无法获取当前学年!", 801);
            }
            var year = nowSemesterTime.Year.Value;

            // 获取该学科的所有知识点
            var allKnowledgePoints = await DBSqlSugar.Queryable<Base_KnowledgePointsDict>()
                .Where(p => p.SubjectId == subjectId && p.IsDeleted == false && p.Type == 1 && p.Version == year.ToString())
                .OrderBy(p => new { p.Level, p.Sort })
                .ToListAsync();

            // 转换为DTO
            var knowledgePointDtos = allKnowledgePoints.Select(kp => new KnowledgePointDto
            {
                Id = kp.Id,
                Content = kp.CodeContent,
                ParentId = kp.ParentId,
                Level = kp.Level,
                Sort = kp.Sort,
                Children = new List<KnowledgePointDto>()
            }).ToList();

            // 构建树形结构
            return BuildKnowledgePointTree(knowledgePointDtos);
        }

        /// <summary>
        /// 构建知识点树形结构
        /// </summary>
        /// <param name="allPoints">所有知识点</param>
        /// <returns></returns>
        private List<KnowledgePointDto> BuildKnowledgePointTree(List<KnowledgePointDto> allPoints)
        {
            // 创建ID到节点的映射
            var pointMap = allPoints.ToDictionary(p => p.Id, p => p);
            var rootPoints = new List<KnowledgePointDto>();

            foreach (var point in allPoints)
            {
                if (string.IsNullOrEmpty(point.ParentId))
                {
                    // 根节点（第一层级）
                    rootPoints.Add(point);
                }
                else
                {
                    // 子节点，添加到父节点的Children中
                    if (pointMap.TryGetValue(point.ParentId, out var parentPoint))
                    {
                        parentPoint.Children.Add(point);
                    }
                }
            }

            // 对每个层级的节点按Sort排序
            SortKnowledgePointTree(rootPoints);

            return rootPoints;
        }

        /// <summary>
        /// 对知识点树进行排序
        /// </summary>
        /// <param name="points">知识点列表</param>
        private void SortKnowledgePointTree(List<KnowledgePointDto> points)
        {
            points.Sort((a, b) => a.Sort.CompareTo(b.Sort));

            foreach (var point in points)
            {
                if (point.Children.Count > 0)
                {
                    SortKnowledgePointTree(point.Children);
                }
            }
        }

        /// <summary>
        /// 根据知识点ID列表获取知识点内容
        /// </summary>
        /// <param name="knowledgePointIds">知识点ID列表</param>
        /// <returns>知识点内容列表</returns>
        private async Task<List<string>> GetKnowledgePointContentsByIds(List<string> knowledgePointIds)
        {
            if (knowledgePointIds == null || knowledgePointIds.Count == 0)
            {
                return new List<string>();
            }

            var knowledgePoints = await DBSqlSugar.Queryable<Base_KnowledgePointsDict>()
                .Where(p => knowledgePointIds.Contains(p.Id) && p.IsDeleted == false)
                .ToListAsync();

            return knowledgePoints.Select(kp => kp.CodeContent).ToList();
        }

        /// <summary>
        /// 根据知识点ID列表获取知识点ID和内容的映射关系
        /// </summary>
        /// <param name="knowledgePointIds">知识点ID列表</param>
        /// <returns>知识点ID和内容的映射字典</returns>
        private async Task<Dictionary<string, string>> GetKnowledgePointMappingByIds(List<string> knowledgePointIds)
        {
            if (knowledgePointIds == null || knowledgePointIds.Count == 0)
            {
                return new Dictionary<string, string>();
            }

            var knowledgePoints = await DBSqlSugar.Queryable<Base_KnowledgePointsDict>()
                .Where(p => knowledgePointIds.Contains(p.Id) && p.IsDeleted == false)
                .ToListAsync();

            return knowledgePoints.ToDictionary(kp => kp.Id, kp => kp.CodeContent);
        }

        /// <summary>
        /// 智能出题（异步）
        /// </summary>
        /// <param name="input"></param>
        /// <returns>任务ID</returns>
        public async Task<string> GenerateQuestionsAsync(IntelligentQuestionGenerationInput input)
        {
            var taskId = Guid.NewGuid().ToString();

            // 异步执行出题任务
            _ = Task.Run(async () =>
            {
                try
                {
                    await ProcessQuestionGeneration(input, taskId);
                }
                catch (Exception ex)
                {
                    // 记录错误日志
                    Console.WriteLine($"出题任务失败: {ex.Message}");
                }
            });

            return taskId;
        }

        /// <summary>
        /// 处理题目生成
        /// </summary>
        /// <param name="input"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        private async Task ProcessQuestionGeneration(IntelligentQuestionGenerationInput input, string taskId)
        {
            Console.WriteLine($"开始生成题目，任务ID: {taskId}");

            // 获取题型信息
            var questionTypes = GetQuestionTypes();
            var selectedTypes = questionTypes.Where(t => input.QuestionTypeIds.Contains(t.Id)).ToList();

            // 使用传入的难度和方向名称，如果为空则使用默认值
            var selectedDifficulty = new DifficultyLevelDto { Name = string.IsNullOrEmpty(input.DifficultyLevelName) ? "中等" : input.DifficultyLevelName };
            var selectedDirection = new QuestionDirectionDto { Name = string.IsNullOrEmpty(input.QuestionDirectionName) ? "综合" : input.QuestionDirectionName };

            // 调用的模型Id
            var modeId = DBSqlSugar.Ado.GetString(@"select Modelkey from AI_ModelBaseInfo where Id=@id", new { id = input.AIModeId });

            // 构建提示词
            var prompt = await BuildPrompt(input, selectedTypes, selectedDifficulty, selectedDirection);

            // 统计信息
            var statistics = new QuestionGenerationStatistics
            {
                TotalCount = input.QuestionCount,
                Status = QuestionGenerationStatus.Started
            };

            // 调用AI服务生成题目
            var questions = await GenerateQuestionsWithAI(selectedTypes, selectedDifficulty, selectedDirection, prompt, modeId);

            // 处理生成的题目
            foreach (var question in questions)
            {
                Console.WriteLine($"生成第{question.QuestionNumber}题: {question.Content}");

                // 更新统计信息
                var existingStat = statistics.TypeStatistics.FirstOrDefault(s => s.TypeId == question.QuestionTypeId);
                if (existingStat != null)
                {
                    existingStat.Count++;
                }
                else
                {
                    var questionType = selectedTypes.FirstOrDefault(t => t.Id == question.QuestionTypeId);
                    statistics.TypeStatistics.Add(new QuestionTypeStatistic
                    {
                        TypeId = question.QuestionTypeId,
                        TypeName = questionType?.Name ?? question.QuestionTypeName,
                        Count = 1
                    });
                }
            }

            // 生成统计文本
            statistics.Status = QuestionGenerationStatus.Completed;
            statistics.StatisticsText = BuildStatisticsText(statistics);

            Console.WriteLine($"题目生成完成: {statistics.StatisticsText}");
        }

        /// <summary>
        /// 构建AI提示词
        /// </summary>
        /// <param name="input"></param>
        /// <param name="selectedTypes"></param>
        /// <param name="selectedDifficulty"></param>
        /// <param name="selectedDirection"></param>
        /// <returns></returns>
        private async Task<string> BuildPrompt(IntelligentQuestionGenerationInput input, List<QuestionTypeDto> selectedTypes, DifficultyLevelDto selectedDifficulty, QuestionDirectionDto selectedDirection)
        {
            var subjectName = DBSqlSugar.Ado.GetString(@"select Name from Exam_Subject where Id=@id", new { id = input.SubjectId });

            // 根据出题模式获取对应的提示词模板
            string directiveKey = GetDirectiveKeyByMode(input.Mode);
            var directive = await DBSqlSugar.Queryable<AI_Directive>()
                .Where(p => p.Key == directiveKey && p.IsDeleted == false)
                .FirstAsync();

            if (directive == null)
            {
                throw new Exception($"无法获取出题提示词模板，Key: {directiveKey}，请联系管理员!");
            }

            var prompt = directive.Directive;

            // 替换通用占位符
            prompt = prompt.Replace("[学段]", BusinessUtil.GetStageByGrade(input.Grade))
                          .Replace("[学科]", subjectName)
                          .Replace("[年级]", BusinessUtil.GradeName(input.Grade))
                          .Replace("[题型]", string.Join("、", selectedTypes.Select(t => t.Name)))
                          .Replace("[难度等级]", string.IsNullOrEmpty(input.DifficultyLevelName) ? "" : input.DifficultyLevelName)
                          .Replace("[出题方向]", string.IsNullOrEmpty(input.QuestionDirectionName) ? "" : input.QuestionDirectionName)
                          .Replace("[题目数量]", input.QuestionCount.ToString());

            // 根据不同模式替换特定占位符
            switch (input.Mode)
            {
                case QuestionGenerationMode.KnowledgePoint:
                    // 根据知识点ID查询知识点内容
                    var knowledgePointContents = await GetKnowledgePointContentsByIds(input.KnowledgePointIds);
                    prompt = prompt.Replace("[知识点]", string.Join("、", knowledgePointContents));
                    break;

                case QuestionGenerationMode.Text:
                    prompt = prompt.Replace("[文本内容]", input.TextContent);
                    break;

                case QuestionGenerationMode.Attachment:
                    // 文档解析：先调用AI解析文档内容
                    List<string> fileUrlsToProcess = new List<string>();

                    // 支持新的多文件格式和旧的单文件格式
                    if (input.FileUrls != null && input.FileUrls.Count > 0)
                    {
                        // 新格式：多文件支持（最多3个）
                        fileUrlsToProcess.AddRange(input.FileUrls.Take(3));
                    }
                    else if (!string.IsNullOrEmpty(input.FileUrl))
                    {
                        // 旧格式：单文件兼容
                        fileUrlsToProcess.Add(input.FileUrl);
                    }

                    List<FileAnalysisDouBaoOutput> fileAnalyses = await _agentCommonService.FileAnalysis(
                        new FileAnalysisInput() {
                            Msg = fileUrlsToProcess
                        });

                    // 将解析结果拼接为附件内容
                    var attachmentContent = fileAnalyses.Count > 0 ?
                        string.Join('。', fileAnalyses.Select(p => p.Content)) : "暂无";

                    prompt = prompt.Replace("[附件内容]", attachmentContent);
                    break;

                case QuestionGenerationMode.Chapter:
                    // 支持新的多章节格式和旧的单章节格式
                    string chapterInfo = "";
                    if (input.ChapterIds != null && input.ChapterIds.Count > 0)
                    {
                        // 新格式：多章节支持
                        var chapterMapping = await GetChapterMappingByIds(input.ChapterIds);
                        chapterInfo = string.Join("、", chapterMapping.Values);
                    }
                    else if (!string.IsNullOrEmpty(input.ChapterName))
                    {
                        // 旧格式：单章节兼容
                        chapterInfo = input.ChapterName;
                    }

                    prompt = prompt.Replace("[章节名称]", chapterInfo)
                                  .Replace("[补充内容]", input.AdditionalRequirements ?? "暂无");
                    break;
            }

            // 处理补充要求占位符
            if (!string.IsNullOrEmpty(input.AdditionalRequirements))
            {
                prompt = prompt.Replace("[补充要求]", $"补充要求：{input.AdditionalRequirements}");
            }
            else
            {
                prompt = prompt.Replace("[补充要求]", "");
            }

            return prompt;
        }

        /// <summary>
        /// 根据出题模式获取对应的提示词Key
        /// </summary>
        /// <param name="mode"></param>
        /// <returns></returns>
        private string GetDirectiveKeyByMode(QuestionGenerationMode mode)
        {
            return mode switch
            {
                QuestionGenerationMode.KnowledgePoint => AIAgentKeys.IntelligentQuestionKnowledgePoint_Directive,
                QuestionGenerationMode.Text => AIAgentKeys.IntelligentQuestionText_Directive,
                QuestionGenerationMode.Attachment => AIAgentKeys.IntelligentQuestionAttachment_Directive,
                QuestionGenerationMode.Chapter => AIAgentKeys.IntelligentQuestionChapter_Directive,
                _ => throw new Exception($"不支持的出题模式: {mode}")
            };
        }

        /// <summary>
        /// 生成题目（调用AI服务）
        /// </summary>
        /// <param name="selectedTypes"></param>
        /// <param name="selectedDifficulty"></param>
        /// <param name="selectedDirection"></param>
        /// <param name="prompt"></param>
        /// <param name="modeId"></param>
        /// <returns></returns>
        private async Task<List<QuestionGenerationResult>> GenerateQuestionsWithAI(List<QuestionTypeDto> selectedTypes, DifficultyLevelDto selectedDifficulty, QuestionDirectionDto selectedDirection, string prompt, string modeId)
        {
            try
            {
                // 调用AI服务生成题目
                var aiInput = new AIGenerateQuestionsInput
                {
                    Prompt = prompt,
                    Model = modeId
                };

                var aiOutput = await _agentCommonService.AIGenerateQuestions(aiInput);

                var results = new List<QuestionGenerationResult>();

                if (aiOutput?.Questions != null)
                {
                    for (int i = 0; i < aiOutput.Questions.Count; i++)
                    {
                        var aiQuestion = aiOutput.Questions[i];
                        var result = new QuestionGenerationResult
                        {
                            QuestionId = Guid.NewGuid().ToString(),
                            QuestionNumber = i + 1,
                            QuestionTypeName = aiQuestion.QuestionType ?? "",
                            Content = aiQuestion.Title ?? "",
                            CorrectAnswer = aiQuestion.Answer ?? "",
                            AnswerExplanation = aiQuestion.Analysis ?? "",
                            DifficultyLevel = selectedDifficulty?.Name ?? "",
                            QuestionDirection = selectedDirection?.Name ?? ""
                        };

                        // 根据题型名称设置题型ID
                        var matchedType = selectedTypes.FirstOrDefault(t => t.Name == aiQuestion.QuestionType);
                        result.QuestionTypeId = matchedType?.Id ?? 0;

                        // 处理选项
                        if (aiQuestion.Options != null && aiQuestion.Options.Count > 0)
                        {
                            result.Options = new List<QuestionOption>();
                            foreach (var option in aiQuestion.Options)
                            {
                                var questionOption = new QuestionOption
                                {
                                    Label = option.Option ?? "",
                                    Content = option.Content ?? "",
                                    IsCorrect = false // 这里可以根据正确答案来判断
                                };

                                // 判断是否为正确答案
                                if (!string.IsNullOrEmpty(result.CorrectAnswer) && !string.IsNullOrEmpty(option.Option))
                                {
                                    questionOption.IsCorrect = result.CorrectAnswer.Contains(option.Option);
                                }

                                result.Options.Add(questionOption);
                            }
                        }

                        results.Add(result);
                    }
                }

                return results;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AI生成题目失败: {ex.Message}");
                // 如果AI生成失败，返回空列表
                return new List<QuestionGenerationResult>();
            }
        }

        /// <summary>
        /// 智能出题（SSE流式返回）
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task GenerateQuestionsStream(IntelligentQuestionGenerationInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                // 1. 发送开始进度
                if (input.Mode == QuestionGenerationMode.Attachment)
                {
                    // 计算文件数量
                    int fileCount = 0;
                    if (input.FileUrls != null && input.FileUrls.Count > 0)
                    {
                        fileCount = Math.Min(input.FileUrls.Count, 3); // 最多3个文件
                    }
                    else if (!string.IsNullOrEmpty(input.FileUrl))
                    {
                        fileCount = 1;
                    }

                    string progressMessage = fileCount > 1
                        ? $"正在解析{fileCount}个文档内容..."
                        : "正在解析文档内容...";
                    //await SendProgress(dataHandler, 0, input.QuestionCount, progressMessage);
                }
                else
                {
                   // await SendProgress(dataHandler, 0, input.QuestionCount, "正在调用AI生成题目...");
                }

                // 2. 获取配置和参数
                var selectedTypes = GetQuestionTypes().Where(t => input.QuestionTypeIds.Contains(t.Id)).ToList();

                if(!input.DifficultyId.IsNullOrEmpty())
                {
                    input.DifficultyLevelName = DBSqlSugar.Ado.GetString(@"select top 1 CodeContent from Exam_ItemCodeProperty(nolock) where Id=@id", new { id = input.DifficultyId });
                }
                if(!input.LearningLevelId.IsNullOrEmpty())
                {
                    input.QuestionDirectionName = DBSqlSugar.Ado.GetString(@"select top 1 CodeContent from Exam_ItemCodeProperty(nolock) where Id=@id", new { id = input.LearningLevelId });
                }

                // 使用传入的难度和方向名称，如果为空则使用默认值
                var selectedDifficulty = new DifficultyLevelDto { Name = string.IsNullOrEmpty(input.DifficultyLevelName) ? "" : input.DifficultyLevelName };
                var selectedDirection = new QuestionDirectionDto { Name = string.IsNullOrEmpty(input.QuestionDirectionName) ? "" : input.QuestionDirectionName };

                // 获取模型ID
                string modeId = DBSqlSugar.Ado.GetString(@"select Modelkey from AI_ModelBaseInfo where Id=@id", new { id = input.AIModeId });

                // 3. 构建提示词（如果是文档出题，这里会先进行文档解析）
                if (input.Mode == QuestionGenerationMode.Attachment)
                {
                    //await SendProgress(dataHandler, 10, input.QuestionCount, "文档解析完成，正在构建出题提示词...");
                }

                string prompt = await BuildPrompt(input, selectedTypes, selectedDifficulty, selectedDirection);

                if (input.Mode == QuestionGenerationMode.Attachment)
                {
                   // await SendProgress(dataHandler, 20, input.QuestionCount, "提示词构建完成，正在调用AI生成题目...");
                }

                // 4. 使用流式AI调用，实时推送题目
                var allQuestions = new List<AIGeneratedQuestion>();
                int currentQuestionIndex = 0;

                // 构建AI输入参数
                var aiInput = new AIGenerateQuestionsInput
                {
                    Prompt = prompt,
                    Model = modeId
                };

                // 如果是知识点出题，添加知识点信息
                if (input.Mode == QuestionGenerationMode.KnowledgePoint && input.KnowledgePointIds != null && input.KnowledgePointIds.Count > 0)
                {
                    var knowledgePointMapping = await GetKnowledgePointMappingByIds(input.KnowledgePointIds);
                    aiInput.IsKnowledgePointMode = true;
                    aiInput.KnowledgePointMapping = knowledgePointMapping;
                }

                // 如果是章节出题，添加章节信息
                if (input.Mode == QuestionGenerationMode.Chapter && input.ChapterIds != null && input.ChapterIds.Count > 0)
                {
                    var chapterMapping = await GetChapterMappingByIds(input.ChapterIds);
                    aiInput.IsChapterMode = true;
                    aiInput.ChapterMapping = chapterMapping;
                }

                await _agentCommonService.AIGenerateQuestionsStream(
                    aiInput,
                    onQuestionGenerated: async (question, questionIndex) =>
                    {
                        if (cancellationToken.IsCancellationRequested)
                        {
                            return;
                        }

                        // 根据题型名称设置题型ID
                        var matchedType = selectedTypes.FirstOrDefault(t => t.Name == question.QuestionType);

                        var result = new AIGeneratedQuestion
                        {
                            QuestionType = question.QuestionType ?? "",
                            Title = question.Title ?? "",
                            QuestionTypeId = question.QuestionTypeId ?? "",
                            Answer = question.Answer ?? "",
                            Analysis = question.Analysis ?? "",
                            Options = question.Options,
                            KnowledgePoints = question.KnowledgePoints, // AI直接返回的知识点信息
                            Chapters = question.Chapters // AI返回的章节信息
                        };

                        allQuestions.Add(result);
                        currentQuestionIndex++;

                        // 立即发送题目给前端
                        await SendQuestion(dataHandler, result, currentQuestionIndex, input.QuestionCount);
                    },
                    onProgress: async (progressMessage) =>
                    {
                        if (!cancellationToken.IsCancellationRequested)
                        {
                            // 发送AI生成进度
                            await SendProgress(dataHandler, currentQuestionIndex, input.QuestionCount, progressMessage);
                        }
                    },
                    cancellationToken: cancellationToken
                );

                if (allQuestions.Count == 0)
                {
                    throw new Exception("AI未返回任何题目");
                }

                // 6. 发送统计信息
                await SendStatistics(dataHandler, allQuestions);

                // 7. 发送完成信号
                //string completeMessage = input.Mode == QuestionGenerationMode.Attachment ?
                //    $"文档解析完成，成功生成{allQuestions.Count}道题目" :
                //    $"成功生成{allQuestions.Count}道题目";
                //await SendComplete(dataHandler, completeMessage);
            }
            catch (OperationCanceledException)
            {
                await SendError(dataHandler, "题目生成已取消");
            }
            catch (Exception ex)
            {
                await SendError(dataHandler, $"题目生成失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 一次性调用AI生成所有题目
        /// </summary>
        /// <param name="input">出题输入参数</param>
        /// <param name="selectedTypes"></param>
        /// <param name="prompt"></param>
        /// <param name="modeId"></param>
        /// <returns></returns>
        private async Task<AIGenerateQuestionsOutput> GenerateAllQuestionsWithAI(IntelligentQuestionGenerationInput input, List<QuestionTypeDto> selectedTypes, string prompt, string modeId)
        {
            try
            {
                // 调用AI服务
                var aiInput = new AIGenerateQuestionsInput
                {
                    Prompt = prompt,
                    Model = modeId,
                    IsKnowledgePointMode = input.Mode == QuestionGenerationMode.KnowledgePoint,
                    IsChapterMode = input.Mode == QuestionGenerationMode.Chapter
                };

                // 如果是知识点出题，添加知识点信息
                if (input.Mode == QuestionGenerationMode.KnowledgePoint && input.KnowledgePointIds != null && input.KnowledgePointIds.Count > 0)
                {
                    var knowledgePointMapping = await GetKnowledgePointMappingByIds(input.KnowledgePointIds);
                    aiInput.AvailableKnowledgePoints = knowledgePointMapping.Values.ToList();
                    aiInput.KnowledgePointMapping = knowledgePointMapping;
                }

                // 如果是章节出题，添加章节信息
                if (input.Mode == QuestionGenerationMode.Chapter && input.ChapterIds != null && input.ChapterIds.Count > 0)
                {
                    var chapterMapping = await GetChapterMappingByIds(input.ChapterIds);
                    aiInput.AvailableChapters = chapterMapping.Values.ToList();
                    aiInput.ChapterMapping = chapterMapping;
                }

                var aiOutput = await _agentCommonService.AIGenerateQuestions(aiInput);

                var results = new List<AIGeneratedQuestion>();

                if (aiOutput?.Questions != null)
                {
                    foreach (var aiQuestion in aiOutput.Questions)
                    {
                        // 根据题型名称设置题型ID
                        var matchedType = selectedTypes.FirstOrDefault(t => t.Name == aiQuestion.QuestionType);

                        var result = new AIGeneratedQuestion
                        {
                            QuestionType = aiQuestion.QuestionType ?? "",
                            Title = aiQuestion.Title ?? "",
                            QuestionTypeId = aiQuestion.QuestionTypeId ?? "",
                            Answer = aiQuestion.Answer ?? "",
                            Analysis = aiQuestion.Analysis ?? "",
                            Options = aiQuestion.Options,
                            KnowledgePoints = aiQuestion.KnowledgePoints // AI直接返回的知识点信息
                        };

                        results.Add(result);
                    }
                }

                return new AIGenerateQuestionsOutput
                {
                    Questions = results,
                    CreateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AI生成题目失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 根据章节ID获取章节名称
        /// </summary>
        /// <param name="chapterId"></param>
        /// <returns></returns>
        private async Task<string> GetChapterNameById(string chapterId)
        {
            if (string.IsNullOrEmpty(chapterId))
            {
                return "未指定章节";
            }

            try
            {
                // 查询章节名称
                var chapterName = await DBSqlSugar.Queryable<Exam_SubjectChapter>()
                    .Where(p => p.Id == chapterId)
                    .Select(p => p.ChapterName)
                    .FirstAsync();

                return string.IsNullOrEmpty(chapterName) ? "未知章节" : chapterName;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询章节名称失败: {ex.Message}");
                return "章节查询失败";
            }
        }

        /// <summary>
        /// 根据章节ID列表获取章节信息映射
        /// </summary>
        /// <param name="chapterIds"></param>
        /// <returns></returns>
        private async Task<Dictionary<string, string>> GetChapterMappingByIds(List<string> chapterIds)
        {
            var mapping = new Dictionary<string, string>();

            if (chapterIds == null || chapterIds.Count == 0)
            {
                return mapping;
            }

            try
            {
                // 查询章节信息
                var chapters = await DBSqlSugar.Queryable<Exam_SubjectChapter>()
                    .Where(p => chapterIds.Contains(p.Id))
                    .Select(p => new { p.Id, p.ChapterName })
                    .ToListAsync();

                foreach (var chapter in chapters)
                {
                    if (!string.IsNullOrEmpty(chapter.Id) && !string.IsNullOrEmpty(chapter.ChapterName))
                    {
                        mapping[chapter.Id] = chapter.ChapterName;
                    }
                }

                return mapping;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查询章节信息失败: {ex.Message}");
                return mapping;
            }
        }

        /// <summary>
        /// 发送进度信息
        /// </summary>
        /// <param name="dataHandler"></param>
        /// <param name="current"></param>
        /// <param name="total"></param>
        /// <param name="description"></param>
        /// <returns></returns>
        private async Task SendProgress(Func<string, Task> dataHandler, int current, int total, string description)
        {
            var output = new QuestionGenerationSSEOutput
            {
                Success = true,
                MessageType = "progress",
                Progress = new QuestionGenerationProgress
                {
                    Current = current,
                    Total = total,
                    Description = description
                }
            };
            string data = "data: " + Newtonsoft.Json.JsonConvert.SerializeObject(output) + "\n\n";
            await dataHandler(data);
        }

        /// <summary>
        /// 发送题目信息
        /// </summary>
        /// <param name="dataHandler"></param>
        /// <param name="question"></param>
        /// <param name="current"></param>
        /// <param name="total"></param>
        /// <returns></returns>
        private async Task SendQuestion(Func<string, Task> dataHandler, AIGeneratedQuestion question, int current, int total)
        {
            var output = new QuestionGenerationSSEOutput
            {
                Success = true,
                MessageType = "question",
                Question = question,
                Progress = new QuestionGenerationProgress
                {
                    Current = current,
                    Total = total,
                    Description = $"已生成第{current}题"
                }
            };
            string data = "data: " + Newtonsoft.Json.JsonConvert.SerializeObject(output) + "\n\n";
            await dataHandler(data);
        }

        /// <summary>
        /// 发送统计信息
        /// </summary>
        /// <param name="dataHandler"></param>
        /// <param name="questions"></param>
        /// <returns></returns>
        private async Task SendStatistics(Func<string, Task> dataHandler, List<AIGeneratedQuestion> questions)
        {
            // 统计题型分布
            var typeDistribution = questions
                .GroupBy(q => q.QuestionType)
                .Select(g => $"{g.Key}: {g.Count()}道")
                .ToList();

            // 生成统计文本
            string statisticsText = $"题目统计信息：\n" +
                $"- 总题目数：{questions.Count}道\n" +
                $"- 题型分布：{string.Join("，", typeDistribution)}\n" +
                $"- 生成时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}";

            var output = new QuestionGenerationSSEOutput
            {
                Success = true,
                MessageType = "statistics",
                StatisticsText = statisticsText
            };

            string data = "data: " + Newtonsoft.Json.JsonConvert.SerializeObject(output) + "\n\n";
            await dataHandler(data);
        }

        /// <summary>
        /// 发送完成信息
        /// </summary>
        /// <param name="dataHandler"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        private async Task SendComplete(Func<string, Task> dataHandler, string message)
        {
            var output = new QuestionGenerationSSEOutput
            {
                Success = true,
                MessageType = "complete",
                Content = message
            };
            string data = "data: " + Newtonsoft.Json.JsonConvert.SerializeObject(output) + "\n\n";
            await dataHandler(data);
        }

        /// <summary>
        /// 发送错误信息
        /// </summary>
        /// <param name="dataHandler"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        private async Task SendError(Func<string, Task> dataHandler, string message)
        {
            var output = new QuestionGenerationSSEOutput
            {
                Success = false,
                MessageType = "error",
                Content = message
            };
            string data = "data: " + Newtonsoft.Json.JsonConvert.SerializeObject(output) + "\n\n";
            await dataHandler(data);
        }

        /// <summary>
        /// 构建统计文本
        /// </summary>
        /// <param name="statistics"></param>
        /// <returns></returns>
        private static string BuildStatisticsText(QuestionGenerationStatistics statistics)
        {
            var text = $"AI一共为您生成了{statistics.TotalCount}道题";

            if (statistics.TypeStatistics.Count > 0)
            {
                var typeTexts = statistics.TypeStatistics.Select(s => $"{s.TypeName}{s.Count}道");
                text += " " + string.Join(" ", typeTexts);
            }

            return text;
        }

        /// <summary>
        /// 重新生成单题
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<RegenerateQuestionOutput> RegenerateQuestion(RegenerateQuestionInput input)
        {
            try
            {
                // 构建重新生成的提示词
                var prompt = await BuildRegeneratePrompt(input);

                // 获取模型ID
                string modeId = DBSqlSugar.Ado.GetString(@"select Modelkey from AI_ModelBaseInfo where Id=@id", new { id = input.AIModeId });

                // 调用AI生成题目
                var aiInput = new AIGenerateQuestionsInput
                {
                    Prompt = prompt,
                    Model = modeId
                };

                var aiOutput = await _agentCommonService.AIGenerateQuestions(aiInput);

                if (aiOutput?.Questions != null && aiOutput.Questions.Count > 0)
                {
                    return new RegenerateQuestionOutput
                    {
                        Success = true,
                        Question = aiOutput.Questions.First()
                    };
                }
                else
                {
                    return new RegenerateQuestionOutput
                    {
                        Success = false,
                        ErrorMessage = "AI生成题目失败，请重试"
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重新生成题目失败: {ex.Message}");
                return new RegenerateQuestionOutput
                {
                    Success = false,
                    ErrorMessage = $"生成失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 构建重新生成题目的提示词
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<string> BuildRegeneratePrompt(RegenerateQuestionInput input)
        {
            // 从 AI_Directive 表中获取重新生成题目的提示词模板
            var directive = await DBSqlSugar.Queryable<AI_Directive>()
                .Where(p => p.Key == AIAgentKeys.IntelligentQuestionRegenerate_Directive && p.IsDeleted == false)
                .FirstAsync();

            if (directive == null)
            {
                throw new Exception($"无法获取重新生成题目提示词模板，Key: {AIAgentKeys.IntelligentQuestionRegenerate_Directive}，请联系管理员!");
            }

            var prompt = directive.Directive;

            // 替换原题目信息占位符
            prompt = prompt.Replace("[原题目类型]", input.OriginalQuestion.QuestionType)
                          .Replace("[原题目题干]", input.OriginalQuestion.Title)
                          .Replace("[原题目答案]", input.OriginalQuestion.Answer)
                          .Replace("[原题目解析]", input.OriginalQuestion.Analysis);

            // 处理原题目选项
            if (input.OriginalQuestion.Options != null && input.OriginalQuestion.Options.Count > 0)
            {
                var optionsText = "";
                foreach (var option in input.OriginalQuestion.Options)
                {
                    optionsText += $"{option.Option}. {option.Content}\n";
                }
                prompt = prompt.Replace("[原题目选项]", $"- 题目选项：\n{optionsText}");
            }
            else
            {
                prompt = prompt.Replace("[原题目选项]", "");
            }

            // 替换用户要求
            prompt = prompt.Replace("[用户要求]", input.UserRequirement ?? "无特殊要求");

            if (!input.DifficultyId.IsNullOrEmpty())
            {
                input.DifficultyLevelName = DBSqlSugar.Ado.GetString(@"select top 1 CodeContent from Exam_ItemCodeProperty(nolock) where Id=@id", new { id = input.DifficultyId });
            }
            if (!input.LearningLevelId.IsNullOrEmpty())
            {
                input.QuestionDirectionName = DBSqlSugar.Ado.GetString(@"select top 1 CodeContent from Exam_ItemCodeProperty(nolock) where Id=@id", new { id = input.LearningLevelId });
            }
            // 替换难度和方向
            prompt = prompt.Replace("[难度等级]", string.IsNullOrEmpty(input.DifficultyLevelName) ? "" : input.DifficultyLevelName)
                          .Replace("[出题方向]", string.IsNullOrEmpty(input.QuestionDirectionName) ? "" : input.QuestionDirectionName);

            return prompt;
        }

        /// <summary>
        /// 单题保存到题库
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<SaveToQuestionBankOutput> SaveSingleQuestionToBank(SaveSingleQuestionToBankInput input)
        {
            try
            {
                // 参数验证
                if (input.Question == null)
                {
                    return new SaveToQuestionBankOutput
                    {
                        Success = false,
                        ErrorMessage = "题目信息不能为空"
                    };
                }

                //if (string.IsNullOrEmpty(input.ChapterId))
                //{
                //    return new SaveToQuestionBankOutput
                //    {
                //        Success = false,
                //        ErrorMessage = "章节ID不能为空"
                //    };
                //}

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }
                var year = nowSemesterTime.Year.Value;
                var term = nowSemesterTime.NowTerm.Value;

                Base_User user = DBSqlSugar.Queryable<Base_User>().Where(x => x.Id == input.UserId && x.Deleted == false).First();
                if (user == null)
                {
                    return new SaveToQuestionBankOutput
                    {
                        Success = false,
                        ErrorMessage = "用户不存在或已被删除"
                    };
                }

                // 保存单个题目
                var questionId = await SaveQuestionToDatabase(input.Question, input.ChapterId, input.DifficultyId, input.LearningLevelId, input.UserId, year, term, user.AreaId);

                if (!string.IsNullOrEmpty(questionId))
                {
                    return new SaveToQuestionBankOutput
                    {
                        Success = true,
                        SuccessCount = 1,
                        FailedCount = 0,
                        SavedQuestionIds = new List<string> { questionId }
                    };
                }
                else
                {
                    return new SaveToQuestionBankOutput
                    {
                        Success = false,
                        SuccessCount = 0,
                        FailedCount = 1,
                        ErrorMessage = "保存题目失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new SaveToQuestionBankOutput
                {
                    Success = false,
                    SuccessCount = 0,
                    FailedCount = 1,
                    ErrorMessage = $"保存失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 批量保存到题库
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<SaveToQuestionBankOutput> SaveBatchQuestionsToBank(SaveBatchQuestionsToBankInput input)
        {
            try
            {
                // 参数验证
                if (input.Questions == null || input.Questions.Count == 0)
                {
                    return new SaveToQuestionBankOutput
                    {
                        Success = false,
                        ErrorMessage = "题目列表不能为空"
                    };
                }

                //if (string.IsNullOrEmpty(input.ChapterId))
                //{
                //    return new SaveToQuestionBankOutput
                //    {
                //        Success = false,
                //        ErrorMessage = "章节ID不能为空"
                //    };
                //}

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                var savedQuestionIds = new List<string>();
                var successCount = 0;
                var failedCount = 0;
                var year= nowSemesterTime.Year.Value;
                var term= nowSemesterTime.NowTerm.Value;

                Base_User user = DBSqlSugar.Queryable<Base_User>().Where(x => x.Id == input.UserId && x.Deleted == false).First();
                if (user == null)
                {
                    return new SaveToQuestionBankOutput
                    {
                        Success = false,
                        ErrorMessage = "用户不存在或已被删除"
                    };
                }
                var areaId= user.AreaId;

                // 逐个保存题目
                foreach (var question in input.Questions)
                {
                    try
                    {
                        var questionId = await SaveQuestionToDatabase(question, input.ChapterId, input.DifficultyId, input.LearningLevelId, input.UserId, year, term, areaId);
                        if (!string.IsNullOrEmpty(questionId))
                        {
                            savedQuestionIds.Add(questionId);
                            successCount++;
                        }
                        else
                        {
                            failedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"保存题目失败: {ex.Message}");
                        failedCount++;
                    }
                }

                return new SaveToQuestionBankOutput
                {
                    Success = successCount > 0,
                    SuccessCount = successCount,
                    FailedCount = failedCount,
                    SavedQuestionIds = savedQuestionIds,
                    ErrorMessage = failedCount > 0 ? $"部分题目保存失败，成功{successCount}个，失败{failedCount}个" : null
                };
            }
            catch (Exception ex)
            {
                return new SaveToQuestionBankOutput
                {
                    Success = false,
                    SuccessCount = 0,
                    FailedCount = input.Questions?.Count ?? 0,
                    ErrorMessage = $"批量保存失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 保存题目到数据库
        /// </summary>
        /// <param name="question">题目信息</param>
        /// <param name="chapterId">章节ID</param>
        /// <param name="difficultyId">难度ID</param>
        /// <param name="learningLevelId">学习水平ID</param>
        /// <param name="userId">创建人Id</param>
        /// <param name="year">学年</param>
        /// <param name="term">学期</param>
        /// <param name="areaId">区域ID</param>
        /// <returns>保存成功的题目ID</returns>
        private async Task<string> SaveQuestionToDatabase(AIGeneratedQuestion question, string chapterId, string difficultyId, string learningLevelId, string userId, int year, int term, int areaId)
        {
            try
            {
                // 开启事务
                DBSqlSugar.BeginTran();

                var subjectId = "";
                var grade = 0;
                if (string.IsNullOrEmpty(chapterId))
                {
                    chapterId = question.Chapters?.FirstOrDefault()?.Id; // 如果AI返回了章节信息，优先使用
                }

                var unitInfo = DBSqlSugar.Ado.SqlQuery<Exam_SubjectChapter>(@"select sc1.* from Exam_SubjectChapter(nolock) sc
                    join Exam_SubjectChapter(nolock) sc1 on sc1.Id= sc.ParentId
                    where sc.Id=@id", new { id = chapterId }).FirstOrDefault();

                grade = unitInfo.ParentId.ToInt32().Value;
                // 1. 根据章节ID获取学科ID
                var subjectInfo = await DBSqlSugar.Queryable<Exam_SubjectChapter>()
                    .Where(c => c.Id == chapterId)
                    .FirstAsync();

                if (subjectInfo == null)
                {
                    throw new Exception("无法获取学科信息");
                }
                subjectId = subjectInfo.SubjectId;

                var iItemType = DBSqlSugar.Ado.GetInt(@"select ItemType from Exam_ItemType(nolock) where ItemTypeName=@typeName", new { typeName = question.QuestionType });

                question.Title = question.Title.Replace("\n", "<br/>").Replace("</p>", "").Replace("<p>", "");
                question.Title = HttpUtility.HtmlDecode(question.Title);
                question.Title = BusinessUtil.ImgAttendEndCode(question.Title);
                question.Title = question.Title.Replace("<br>", "<br/>");
                var itemArray = GetItemListSplit(question.Title, iItemType);
                if (itemArray.Length > 0 && itemArray[0] != null)
                {
                    question.Title = itemArray[0];
                }

                // 2. 生成题目ID
                var questionId = Guid.NewGuid().ToString();

                // 3. 保存到Exam_Question主表
                var examQuestion = new Exam_Question
                {
                    Id = questionId,
                    Title = question.Title,
                    Year = year,
                    GradeId = grade,
                    Semester = term,
                    QuestionTypeId = GetQuestionTypeId(question.QuestionType),
                    SubjectId = subjectId,
                    ChapterId = chapterId,
                    Analysis = question.Analysis,
                    CreatorId = userId,
                    AreaId = areaId,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    IsDeleted = false,
                    SourceType = 4,
                    State = 1, // 未发布状态
                    PlatformSource = 1 // 专科专练
                };

                await DBSqlSugar.Insertable(examQuestion).ExecuteCommandAsync();

                // 4. 保存答案到Exam_QuestionConfig
                if (!string.IsNullOrEmpty(question.Answer))
                {
                    var answerConfig = new Exam_QuestionConfig
                    {
                        Id = Guid.NewGuid().ToString(),
                        QuestionId = questionId,
                        Content = question.Answer,
                        Type = 1, // 答案类型
                        Sort = 1,
                        CreateTime = DateTime.Now
                    };
                    await DBSqlSugar.Insertable(answerConfig).ExecuteCommandAsync();
                }

                // 5. 保存选项到Exam_QuestionConfig
                if (question.Options != null && question.Options.Count > 0)
                {
                    var optionConfigs = new List<Exam_QuestionConfig>();
                    for (int i = 0; i < question.Options.Count; i++)
                    {
                        var option = question.Options[i];
                        optionConfigs.Add(new Exam_QuestionConfig
                        {
                            Id = Guid.NewGuid().ToString(),
                            QuestionId = questionId,
                            Content = option.Content,
                            Option = option.Option,
                            Type = 3, // 选项类型
                            Sort = i + 1,
                            CreateTime = DateTime.Now
                        });
                    }
                    await DBSqlSugar.Insertable(optionConfigs).ExecuteCommandAsync();
                }

                // 6. 保存难度属性到Exam_QuestionPropertyMapping
                if(!string.IsNullOrEmpty(difficultyId))
                {
                    var difficultyMapping = new Exam_QuestionPropertyMapping
                    {
                        Id = Guid.NewGuid().ToString(),
                        QuestionId = questionId,
                        PropertyId = difficultyId,
                        PropertyType = 1, // 题目难度
                        CreateTime = DateTime.Now
                    };
                    await DBSqlSugar.Insertable(difficultyMapping).ExecuteCommandAsync();
                }


                // 7. 保存学习水平属性到Exam_QuestionPropertyMapping
                if (!string.IsNullOrEmpty(learningLevelId))
                {
                    var learningLevelMapping = new Exam_QuestionPropertyMapping
                    {
                        Id = Guid.NewGuid().ToString(),
                        QuestionId = questionId,
                        PropertyId = learningLevelId,
                        PropertyType = 2, // 学习水平
                        CreateTime = DateTime.Now
                    };
                    await DBSqlSugar.Insertable(learningLevelMapping).ExecuteCommandAsync();
                }
                 

                // 8. 保存知识点属性到Exam_QuestionPropertyMapping（如果有知识点信息）
                if (question.KnowledgePoints != null && question.KnowledgePoints.Count > 0)
                {
                    var knowledgePointMappings = new List<Exam_QuestionPropertyMapping>();
                    foreach (var knowledgePoint in question.KnowledgePoints)
                    {
                        if (!string.IsNullOrEmpty(knowledgePoint.Id))
                        {
                            knowledgePointMappings.Add(new Exam_QuestionPropertyMapping
                            {
                                Id = Guid.NewGuid().ToString(),
                                QuestionId = questionId,
                                PropertyId = knowledgePoint.Id,
                                PropertyType = 7, // 知识点
                                CreateTime = DateTime.Now
                            });
                        }
                    }

                    if (knowledgePointMappings.Count > 0)
                    {
                        await DBSqlSugar.Insertable(knowledgePointMappings).ExecuteCommandAsync();
                    }
                }

                // 提交事务
                DBSqlSugar.CommitTran();

                return questionId;
            }
            catch (Exception ex)
            {
                // 回滚事务
                DBSqlSugar.RollbackTran();
                Console.WriteLine($"保存题目到数据库失败: {ex.Message}");
                throw;
            }
        }

        public static int EvaluatorNum = 0;
        public static string TeachAnswer = "";

        /// <summary>
        /// 取经过题型进行分割后的数组
        /// </summary>
        /// <param name="itemTitle"></param>
        /// <param name="itemType"></param>
        /// <returns></returns>
        public string[] GetItemListSplit(string itemTitle, int itemType)
        {
            itemTitle = itemTitle.Replace(".obs.cn-east-2.myhuaweicloud.com:443", "!@#$%^&*");
            string[] strArray = new string[3];
            List<string> strList = new List<string>();
            RegexOptions options = RegexOptions.None;
            Regex regex;
            strArray[0] = itemTitle;

            var regexPatternEntity = DBSqlSugar.Queryable<Exam_RegexPattern>().Where(x=>x.Id== "2").First();

            // 单项选择题 = 1  id:2 ,多项选择题 = 2  id:10,判断题 = 3 id:11 ,填空题 = 4 id:5 ,问答=5   id:6 选词填空题
            string pattern = "";
            switch (itemType)
            {
                case 1:
                case 7: //特殊选择题
                    var answerInfo = "";
                    if (itemTitle.Contains("\r答案"))
                    {
                        answerInfo = "答案：" + itemTitle.Split("\r答案：")[1];
                        itemTitle = itemTitle.Split("\r答案：")[0];
                    }

                    strArray[0] = strArray[0].Replace("!@#$%^&*", ".obs.cn-east-2.myhuaweicloud.com:443");
                    pattern = regexPatternEntity.RePattern.Trim();
                    regex = new Regex(pattern, options);
                    strArray = regex.Split(itemTitle);
                    strList.AddRange(strArray.ToList());
                    strList.Add(answerInfo);
                    strArray = strList.ToArray();
                    break;
                case 2:
                    pattern = regexPatternEntity.RePattern.Trim();
                    regex = new Regex(pattern, options);
                    strArray = regex.Split(itemTitle);
                    break;
                case 3:
                    strArray[0] = itemTitle;
                    strArray[1] = "对";
                    strArray[2] = "错";

                    break;
                case 4:
                    regexPatternEntity = DBSqlSugar.Queryable<Exam_RegexPattern>().Where(x => x.Id == "4").First();//_service.GetById<Exam_RegexPattern>("4");
                    pattern = regexPatternEntity.RePattern.Trim();
                    strArray = new string[3];
                    EvaluatorNum = 0;
                    TeachAnswer = "";
                    MatchEvaluator evaluator = new MatchEvaluator(getEvaluator);
                    strArray[0] = Regex.Replace(itemTitle, pattern, evaluator, options);
                    if (TeachAnswer.IsNullOrEmpty() == false && TeachAnswer.Last() == '|')
                    {
                        strArray[1] = TeachAnswer.Substring(0, TeachAnswer.Length - 1);
                    }

                    break;
                case 6:
                    strArray[0] = itemTitle;
                    break;
            }
            for (var i = 0; i < strArray.Length; i++)
            {
                if (!strArray[i].IsNullOrEmpty())
                    strArray[i] = strArray[i].Replace("!@#$%^&*", ".obs.cn-east-2.myhuaweicloud.com:443");
            }
            return strArray;
        }

        public string getEvaluator(Match m)
        {
            string TeachAnswerTemp = m.Value.Replace("&nbsp;", "");
            TeachAnswerTemp = TeachAnswerTemp.Substring(1, TeachAnswerTemp.Length - 1);
            TeachAnswerTemp = TeachAnswerTemp.Substring(0, TeachAnswerTemp.Length - 1);
            //TeachAnswerTemp = TeachAnswerTemp.Replace("）", "");
            TeachAnswer += TeachAnswerTemp.Trim() + "|";
            string outStr = "(#&" + EvaluatorNum.ToString() + "@)";
            EvaluatorNum++;
            return outStr;
        }

        /// <summary>
        /// 根据题目类型名称获取题目类型ID
        /// </summary>
        /// <param name="questionTypeName">题目类型名称</param>
        /// <returns>题目类型ID</returns>
        private int GetQuestionTypeId(string questionTypeName)
        {
            var questionTypes = GetQuestionTypes();
            var questionType = questionTypes.FirstOrDefault(t => t.Name == questionTypeName);
            return questionType?.Id ?? 1; // 默认返回单项选择题
        }
    }
}
