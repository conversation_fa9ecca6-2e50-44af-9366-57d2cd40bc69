﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_学生端语交际
    /// </summary>
    [Route("/AgentStudentOralCommunication/[controller]/[action]")]
    [ApiController]
    public class AgentStudentOralCommunicationController : ApiBaseController<IAgentStudentOralCommunicationService>
    {
        #region DI

        private readonly IAgentCommonService _agentCommonService;
        private readonly IAgentStudentOralCommunicationService _agentStudentOralCommunicationService;
        public AgentStudentOralCommunicationController(
            IAgentStudentOralCommunicationService agentStudentOralCommunicationService,
            IAgentCommonService agentCommonService
            )
        {
            _agentStudentOralCommunicationService = agentStudentOralCommunicationService;
            _agentCommonService = agentCommonService;
        }

        #endregion

        /// <summary>
        /// 智能体_学生端口语交际对话
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task AgentStudentOralCommunicationDialogue(AgentStudentOralCommunicationDialogueInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.AgentId)
                    || string.IsNullOrEmpty(input.AgentTaskId)
                    || string.IsNullOrEmpty(input.StudentId)
                    || string.IsNullOrEmpty(input.ClassId)
                    || (string.IsNullOrEmpty(input.Msg) && string.IsNullOrEmpty(input.AudioUrl)))
                {
                    throw new Exception("参数异常!");
                }
                if (!string.IsNullOrEmpty(input.AudioUrl))
                {
                    string fileExtension = Path.GetExtension(input.AudioUrl);
                    if (fileExtension.ToLower() != ".wav")
                    {
                        throw new Exception("非常抱歉，目前我们仅支持 wav 这一种音频格式，还请您谅解!");
                    }
                    input.Msg = await _agentCommonService.AudioFileChangeText(new AudioFileChangeTextInput()
                    {
                        FileUrl = input.AudioUrl,
                    });
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentStudentOralCommunicationService.AgentStudentOralCommunicationDialogue(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 智能体_学生端口语交际提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task AgentStudentOralCommunicationSubmit(AgentStudentOralCommunicationSubmitInput input)
        {
            if (string.IsNullOrEmpty(input.AgentId)
                        || string.IsNullOrEmpty(input.AgentTaskId)
                        || string.IsNullOrEmpty(input.StudentId)
                        || string.IsNullOrEmpty(input.ClassId))
            {
                throw new Exception("参数异常!");
            }
            await _agentStudentOralCommunicationService.AgentStudentOralCommunicationSubmit(input);
        }
    }
}
