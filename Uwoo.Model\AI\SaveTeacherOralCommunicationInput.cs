﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体_教师端口语交际保存入参
    /// </summary>
    public class SaveTeacherOralCommunicationInput
    {
        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 章节Id
        /// </summary>
        public string? ChapterId { get; set; }

        /// <summary>
        /// 教学目标
        /// </summary>
        public string? Target { get; set; }

        /// <summary>
        /// 评价体系Id
        /// </summary>
        public string? EvaluateId { get; set; }

        /// <summary>
        /// 开场白
        /// </summary>
        public string? Prologue { get; set; }

        /// <summary>
        /// 场景
        /// </summary>
        public string? Scene { get; set; }

        /// <summary>
        /// 场景细节
        /// </summary>
        public string? SceneDetail { get; set; }

        /// <summary>
        /// 互动模式(1指令式、2对话式、3辩论式)
        /// </summary>
        public int? InteractiveMode { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 发布班级Id
        /// </summary>
        public List<string> ClassId { get; set; } = new List<string>();

        /// <summary>
        /// 发布时间范围(下标0开始、下标1结束)
        /// </summary>
        public List<DateTime> TimeRange { get; set; } = new List<DateTime>();

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }

        /// <summary>
        /// 对话式任务
        /// </summary>
        public List<OralCommunicationDialogueTaskInput> DialogueTasks { get; set; } = new List<OralCommunicationDialogueTaskInput>();

        /// <summary>
        /// 指令式任务
        /// </summary>
        public List<OralCommunicationInstructTaskInput> InstructTasks { get; set; } = new List<OralCommunicationInstructTaskInput>();
    }

    /// <summary>
    /// 对话式任务保存入参
    /// </summary>
    public class OralCommunicationDialogueTaskInput
    {
        /// <summary>
        /// Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 对话目标
        /// </summary>
        public string? DialogueTarget { get; set; }

        /// <summary>
        /// 有效回应标准
        /// </summary>
        public string? ValidRespond { get; set; }

        /// <summary>
        /// 追问话术
        /// </summary>
        public string? Asked { get; set; }
    }

    /// <summary>
    /// 指令式任务保存入参
    /// </summary>
    public class OralCommunicationInstructTaskInput
    {
        /// <summary>
        /// Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 指令内容
        /// </summary>
        public string? InstructContent { get; set; }

        /// <summary>
        /// 验证方式（1图片、2动画(Html文件)、3文本)
        /// </summary>
        public int? VerificationMode { get; set; }

        /// <summary>
        /// 图片地址
        /// </summary>
        public List<ImgInput> ImgUrls { get; set; } = new List<ImgInput>();

        /// <summary>
        /// html文件消息
        /// </summary>
        public AIFileInfoDto HtmlFileInfo { get; set; } = new AIFileInfoDto();

        /// <summary>
        /// 成果要求
        /// </summary>
        public string? Result { get; set; }
    }
}
