﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体_教师端口语交际列表输出
    /// </summary>
    public class AgentTeacherOralCommunicationListOutput
    {
        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 项目背景介绍
        /// </summary>
        public string? Introduce { get; set; }

        /// <summary>
        /// 互动模式(1指令式、2对话式、3辩论式)
        /// </summary>
        public int? InteractiveMode { get; set; }

        /// <summary>
        /// 章节Id
        /// </summary>
        public string? ChapterId { get; set; }

        /// <summary>
        /// 章节名称
        /// </summary>
        public string? ChapterName { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public string? BeginTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public string? EndTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string? CreateTime { get; set; }

        /// <summary>
        /// 发布的班级
        /// </summary>
        public string? ClassName { get; set; }

        /// <summary>
        /// Logo
        /// </summary>
        public string? Logo { get; set; }
    }

    /// <summary>
    /// 智能体_教师端口语交际列表班级
    /// </summary>
    public class AgentTeacherOralCommunicationListClass
    {
        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? AgentTaskId { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 班级名称
        /// </summary>
        public string? ClassName { get; set; }
    }
}
