﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取教案列表
    /// </summary>
    public class GetTeachingPlanOutput
    {
        /// <summary>
        /// 教案Id
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 教案名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string? Createtime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 教案类型（1文本、2word）
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 文件Id（用于Office在线编辑）
        /// </summary>
        public string? FileId { get; set; }
    }
}
