﻿using Autofac;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using Uwoo.Core.CacheManager.IService;
using Uwoo.Core.CacheManager.Service;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Lifecycle;
using Uwoo.Core.ObjectActionValidator.ExpressValidator;
using Uwoo.Core.Services;
using Uwoo.Core.UserManager;


//using Uwoo.Core.KafkaManager.IService;
//using Uwoo.Core.KafkaManager.Service;

namespace Uwoo.Core.Extensions.AutofacManager
{
	public static class AutofacContainerModuleExtension
	{
		//  private static bool _isMysql = false;
		public static IServiceCollection AddModule(this IServiceCollection services, ContainerBuilder builder, IConfiguration configuration)
		{
			Type baseType = typeof(IDependency);
			var compilationLibrary = DependencyContext.Default
				.RuntimeLibraries
				.Where(x => !x.Serviceable
				&& x.Type == "project")
				.ToList();
			var count1 = compilationLibrary.Count;
			List<Assembly> assemblyList = new List<Assembly>();

			foreach (var _compilation in compilationLibrary)
			{
				try
				{
					assemblyList.Add(AssemblyLoadContext.Default.LoadFromAssemblyName(new AssemblyName(_compilation.Name)));
				}
				catch (Exception ex)
				{
					Console.WriteLine(_compilation.Name + ex.Message);
				}
			}
			builder.RegisterAssemblyTypes(assemblyList.ToArray())
			 .Where(type => baseType.IsAssignableFrom(type) && !type.IsAbstract)
			 .AsSelf().AsImplementedInterfaces()
			 .InstancePerLifetimeScope();
			builder.RegisterType<UserContext>().InstancePerLifetimeScope();
			builder.RegisterType<ActionObserver>().InstancePerLifetimeScope();
			//model校验结果
			builder.RegisterType<ObjectModelValidatorState>().InstancePerLifetimeScope();
            //启用缓存
            if (AppSetting.UseRedis)
			{
				builder.RegisterType<RedisCacheService>().As<ICacheService>().SingleInstance();

				// 继承单例生存周期的类，自动注册
				var singleton = typeof(ISingletonService);
				var impl_service = singleton.Assembly;
				builder.RegisterAssemblyTypes(impl_service).Where(x => singleton.IsAssignableFrom(x) && x != singleton)
					   .AsImplementedInterfaces()
					   .SingleInstance()
					   .PropertiesAutowired();

				// 继承单例生存周期的类，自动注册
				//var transient= typeof(ITransientService);
				//var transient_service = transient.Assembly;
				//builder.RegisterAssemblyTypes(transient_service).Where(x => transient.IsAssignableFrom(x) && x != singleton)
				//	   .AsImplementedInterfaces()
				//	   .InstancePerLifetimeScope()
				//	   .PropertiesAutowired();
			}
			else
			{
				builder.RegisterType<MemoryCacheService>().As<ICacheService>().SingleInstance();
			}
			//kafka注入
			//if (AppSetting.Kafka.UseConsumer)
			//    builder.RegisterType<KafkaConsumer<string, string>>().As<IKafkaConsumer<string, string>>().SingleInstance();
			//if (AppSetting.Kafka.UseProducer)
			//    builder.RegisterType<KafkaProducer<string, string>>().As<IKafkaProducer<string, string>>().SingleInstance();
			return services;
		}

	}
}
