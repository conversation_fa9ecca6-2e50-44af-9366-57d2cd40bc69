<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能出题SSE测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            display: flex;
            min-height: 600px;
        }

        .form-panel {
            max-width: 550px;
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
        }

        .mode-tabs {
            display: flex;
            margin-bottom: 25px;
            background: #e9ecef;
            border-radius: 8px;
            padding: 4px;
        }

        .mode-tab {
            flex: 1;
            padding: 10px 15px;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .mode-tab:hover {
            color: #495057;
            background: rgba(255, 255, 255, 0.5);
        }

        .mode-tab.active {
            background: white;
            color: #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .mode-content {
            transition: all 0.3s ease;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .results-panel {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .progress-container {
            margin-bottom: 30px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            color: #666;
            font-weight: 600;
        }

        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
            display: none;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .question-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            opacity: 0;
            transform: translateY(20px);
            animation: slideIn 0.5s ease forwards;
        }

        @keyframes slideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .question-number {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
        }

        .question-type {
            background: #f8f9fa;
            color: #666;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
        }

        .question-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .question-options {
            margin-bottom: 15px;
        }

        .option-item {
            padding: 10px 15px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #e9ecef;
        }

        .option-item.correct {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }

        .question-answer {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .question-answer strong {
            color: #856404;
        }

        .question-analysis {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            color: #666;
            line-height: 1.6;
        }

        .question-actions {
            margin-top: 15px;
            text-align: right;
        }

        .btn-regenerate {
            background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-regenerate:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(255, 167, 38, 0.3);
        }

        .btn-regenerate:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 12px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .modal-header {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .modal-body textarea {
            width: 100%;
            height: 100px;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            font-family: inherit;
        }

        .modal-body textarea:focus {
            outline: none;
            border-color: #4facfe;
        }

        .modal-footer {
            margin-top: 20px;
            text-align: right;
        }

        .modal-footer button {
            margin-left: 10px;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-modal-cancel {
            background: #6c757d;
            color: white;
        }

        .btn-modal-confirm {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-modal-cancel:hover,
        .btn-modal-confirm:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* 保存到题库按钮样式 */
        .btn-save-to-bank {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 10px;
        }

        .btn-save-to-bank:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-save-to-bank:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-save-all-to-bank {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 20px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }

        .btn-save-all-to-bank:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(23, 162, 184, 0.3);
        }

        .btn-save-all-to-bank:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .empty-state {
            text-align: center;
            color: #666;
            padding: 60px 20px;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .statistics-panel {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .statistics-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .statistics-content {
            color: #666;
            line-height: 1.8;
            white-space: pre-line;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 知识点标签样式 */
        .question-knowledge-points {
            margin: 15px 0;
            padding: 12px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }

        .knowledge-points-list {
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .knowledge-point-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }

        .knowledge-point-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* 不同层级的知识点标签颜色 */
        .knowledge-point-tag.level-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .knowledge-point-tag.level-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .knowledge-point-tag.level-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .knowledge-point-tag small {
            opacity: 0.8;
            margin-left: 4px;
        }

        /* 流式生成动画效果 */
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(0.8);
            }
        }

        /* 流式生成标识样式 */
        .streaming-badge {
            animation: pulse 2s infinite;
        }

        /* 实时统计面板增强 */
        .statistics-panel {
            transition: all 0.3s ease;
        }

        .statistics-panel.updating {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能出题系统</h1>
            <p>基于AI的智能题目生成，支持实时流式推送</p>
        </div>
        
        <div class="content">
            <!-- 左侧表单面板 -->
            <div class="form-panel">
                <h2>📝 智能出题参数设置</h2>

                <!-- 出题方式选择 -->
                <div class="mode-tabs">
                    <button type="button" class="mode-tab active" data-mode="text">📄 文本出题</button>
                    <button type="button" class="mode-tab" data-mode="chapter">📚 章节出题</button>
                    <button type="button" class="mode-tab" data-mode="document">📎 文档出题</button>
                    <button type="button" class="mode-tab" data-mode="knowledge">🧠 知识点出题</button>
                </div>

                <form id="questionForm">
                    <div class="form-group">
                        <label>题型选择</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="type1" value="1" checked>
                                <label for="type1">单选题</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="type2" value="2">
                                <label for="type2">多选题</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="type3" value="3" checked>
                                <label for="type3">判断题</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="type4" value="4">
                                <label for="type4">填空题</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="type5" value="5">
                                <label for="type5">主观题</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="difficulty">难度等级（选填）</label>
                        <select id="difficulty">
                            <option value="">请选择难度等级（默认：中等）</option>
                            <option value="1">容易</option>
                            <option value="2">较易</option>
                            <option value="3">中等</option>
                            <option value="4">较难</option>
                            <option value="5">困难</option>
                        </select>
                        <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">如果不选择，系统将使用默认难度等级：中等</small>
                    </div>

                    <div class="form-group">
                        <label for="direction">出题方向（选填）</label>
                        <select id="direction">
                            <option value="">请选择出题方向（默认：综合）</option>
                            <option value="1">识记</option>
                            <option value="2">理解</option>
                            <option value="3">掌握</option>
                            <option value="4">应用</option>
                            <option value="5">综合</option>
                        </select>
                        <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">如果不选择，系统将使用默认出题方向：综合</small>
                    </div>

                    <div class="form-group">
                        <label for="questionCount">题目数量</label>
                        <input type="number" id="questionCount" min="1" max="30" value="5" required>
                    </div>

                    <!-- 文本出题内容 -->
                    <div class="mode-content" id="textModeContent">
                        <div class="form-group">
                            <label for="textContent">出题范围文本 (最多500字)</label>
                            <textarea id="textContent" maxlength="500" placeholder="请输入出题范围的文本内容..." required>考查小朋友20以内的加减法运算</textarea>
                        </div>
                    </div>

                    <!-- 章节出题内容 -->
                    <div class="mode-content" id="chapterModeContent" style="display: none;">
                        <div class="form-group">
                            <label for="chapterIds">章节ID列表</label>
                            <textarea id="chapterIds" placeholder="请输入章节ID，每行一个，如：&#10;chapter001&#10;chapter002&#10;chapter003" style="height: 80px;"></textarea>
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">每行输入一个章节ID，支持多个章节</small>
                        </div>

                        <!-- 兼容旧版本的章节名称输入 -->
                        <div class="form-group">
                            <label for="chapterName">章节名称（兼容旧版本）</label>
                            <input type="text" id="chapterName" placeholder="请输入章节名称，如：第一单元-认识数字">
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">如果上面的章节ID为空，将使用此字段（旧格式）</small>
                        </div>

                        <!-- 补充内容（选填） -->
                        <div class="form-group">
                            <label for="additionalRequirements">补充内容（选填）</label>
                            <textarea id="additionalRequirements" maxlength="300" placeholder="请输入补充的出题要求..."></textarea>
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">可以添加特殊要求或注意事项</small>
                        </div>
                    </div>

                    <!-- 文档出题内容 -->
                    <div class="mode-content" id="documentModeContent" style="display: none;">
                        <div class="form-group">
                            <label for="fileUrls">文档URL列表（支持多个文件，最多3个）</label>
                            <textarea id="fileUrls" placeholder="请输入文档URL，每行一个，如：&#10;https://example.com/doc1.pdf&#10;https://example.com/doc2.docx&#10;https://example.com/doc3.jpg" style="height: 80px;"></textarea>
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">每行输入一个文档URL，最多支持3个文件</small>
                        </div>

                        <!-- 兼容旧版本的单文件URL输入 -->
                        <div class="form-group">
                            <label for="fileUrl">单个文档URL（兼容旧版本）</label>
                            <input type="url" id="fileUrl" placeholder="请输入文档的URL地址，如：https://example.com/document.pdf">
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">如果上面的文档URL列表为空，将使用此字段（旧格式）</small>
                        </div>

                        <!-- 补充要求（选填） -->
                        <div class="form-group">
                            <label for="documentRequirements">补充要求（选填）</label>
                            <textarea id="documentRequirements" maxlength="300" placeholder="请输入补充的出题要求，如：重点关注某个知识点、特定题型等..."></textarea>
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">可以添加特殊要求或注意事项</small>
                        </div>

                        <!-- 文档解析提示 -->
                        <div style="background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 6px; padding: 12px; margin-top: 10px;">
                            <div style="color: #0066cc; font-weight: 600; margin-bottom: 5px;">💡 文档出题说明</div>
                            <div style="color: #0066cc; font-size: 13px; line-height: 1.4;">
                                • 系统将先使用AI解析文档内容，提取关键信息<br>
                                • 支持多个文档同时解析（最多3个）<br>
                                • 然后基于解析结果生成相关题目<br>
                                • 整个过程可能需要较长时间，请耐心等待
                            </div>
                        </div>
                    </div>

                    <!-- 知识点出题内容 -->
                    <div class="mode-content" id="knowledgeModeContent" style="display: none;">
                        <div class="form-group">
                            <label for="subjectId">学科选择</label>
                            <select id="subjectId" onchange="loadKnowledgePoints()">
                                <option value="">请选择学科</option>
                                <option value="1">语文</option>
                                <option value="2">数学</option>
                                <option value="3">英语</option>
                                <option value="4">物理</option>
                                <option value="5">化学</option>
                                <option value="6">生物</option>
                                <option value="7">历史</option>
                                <option value="8">地理</option>
                                <option value="9">政治</option>
                            </select>
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">选择学科后将自动加载对应的知识点</small>
                        </div>

                        <div class="form-group">
                            <label for="knowledgePoints">知识点选择</label>
                            <div style="margin-bottom: 5px;">
                                <button type="button" onclick="expandAllKnowledgePoints()" style="font-size: 12px; padding: 2px 8px; margin-right: 5px; background: #e3f2fd; border: 1px solid #90caf9; border-radius: 3px; cursor: pointer;">全部展开</button>
                                <button type="button" onclick="collapseAllKnowledgePoints()" style="font-size: 12px; padding: 2px 8px; background: #fce4ec; border: 1px solid #f48fb1; border-radius: 3px; cursor: pointer;">全部折叠</button>
                            </div>
                            <div id="knowledgePointsContainer" style="border: 1px solid #ddd; border-radius: 4px; padding: 10px; min-height: 120px; max-height: 200px; overflow-y: auto; background: #f9f9f9;">
                                <div style="color: #999; text-align: center; padding: 20px;">请先选择学科</div>
                            </div>
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">支持多选，至少选择一个知识点。点击▶/▼展开/折叠子节点</small>
                        </div>

                        <!-- 补充要求（选填） -->
                        <div class="form-group">
                            <label for="knowledgeRequirements">补充要求（选填）</label>
                            <textarea id="knowledgeRequirements" maxlength="300" placeholder="请输入补充的出题要求，如：重点关注某个概念、特定应用场景等..."></textarea>
                            <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">可以添加特殊要求或注意事项</small>
                        </div>

                        <!-- 知识点出题提示 -->
                        <div style="background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 6px; padding: 12px; margin-top: 10px;">
                            <div style="color: #0369a1; font-weight: 600; margin-bottom: 5px;">🧠 知识点出题说明</div>
                            <div style="color: #0369a1; font-size: 13px; line-height: 1.4;">
                                • 系统将基于选择的知识点生成相关题目<br>
                                • 支持多个知识点组合出题<br>
                                • 题目内容将紧密围绕所选知识点展开
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="aiModeId">AI模型ID</label>
                        <input type="text" id="aiModeId" value="1943219517715267584" required>
                    </div>

                    <div class="form-group">
                        <label for="authToken">认证Token</label>
                        <input type="text" id="authToken" value="Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOiIxNzQyMDIwODI5ODcwMzMzOTUyIiwiVXNlck5hbWUiOiJjc3h4MTAwIiwiU2Nob29sSWQiOm51bGwsIlBsYXRmb3JtIjoxLCJFeHBpcmUiOiIyMDI1LTA3LTIzIDAzOjAwOjAwIn0.grV6aKv5GHRqEozdB5l5YVQuDFAZJB2koTzZx0VwFDs" required>
                        <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">请输入完整的认证Token，包含Bearer前缀</small>
                    </div>

                    <button type="submit" class="btn btn-primary" id="generateBtn">
                        <span id="generateText">🚀 开始生成题目</span>
                    </button>
                    
                    <button type="button" class="btn btn-danger" id="cancelBtn" style="display: none;">
                        ❌ 取消生成
                    </button>
                </form>
            </div>

            <!-- 右侧结果面板 -->
            <div class="results-panel">
                <!-- 进度条 -->
                <div class="progress-container" id="progressContainer">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>

                <!-- 状态消息 -->
                <div class="status-message" id="statusMessage"></div>

                <!-- 统计信息面板 -->
                <div class="statistics-panel" id="statisticsPanel">
                    <div class="statistics-title">
                        📊 生成统计
                    </div>
                    <div class="statistics-content" id="statisticsContent"></div>
                </div>

                <!-- 题目列表 -->
                <div id="questionsList">
                    <div class="empty-state">
                        <div style="font-size: 4em; margin-bottom: 20px; opacity: 0.3;">📝</div>
                        <h3>等待生成题目</h3>
                        <p>请在左侧填写出题参数，然后点击"开始生成题目"</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 换一题模态框 -->
    <div id="regenerateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                🔄 重新生成题目
            </div>
            <div class="modal-body">
                <label for="userRequirement" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                    请输入您的要求（可选）：
                </label>
                <textarea id="userRequirement" placeholder="例如：提高难度、简化题目、更换知识点等..."></textarea>
                <small style="display: block; margin-top: 5px; color: #666; font-size: 12px;">
                    留空则保持原题目的难度和风格
                </small>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-modal-cancel" onclick="closeRegenerateModal()">取消</button>
                <button type="button" class="btn-modal-confirm" onclick="confirmRegenerate()">确定</button>
            </div>
        </div>
    </div>

    <!-- 保存到题库模态框 -->
    <div id="saveToBankModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                💾 保存到题库
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 15px;">
                    <label for="chapterId" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                        章节ID：
                    </label>
                    <input type="text" id="chapterId" placeholder="请输入章节ID" style="width: 100%; padding: 8px; border: 2px solid #e9ecef; border-radius: 6px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label for="difficultyId" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                        难度ID：
                    </label>
                    <input type="text" id="difficultyId" placeholder="请输入难度ID" style="width: 100%; padding: 8px; border: 2px solid #e9ecef; border-radius: 6px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label for="learningLevelId" style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                        学习水平ID：
                    </label>
                    <input type="text" id="learningLevelId" placeholder="请输入学习水平ID" style="width: 100%; padding: 8px; border: 2px solid #e9ecef; border-radius: 6px;">
                </div>

                <!-- 知识点信息显示区域 -->
                <div id="knowledgePointsInfo" style="margin-bottom: 15px; display: none;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                        🧠 关联的知识点：
                    </label>
                    <div id="knowledgePointsList" style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; max-height: 120px; overflow-y: auto;">
                        <!-- 知识点列表将在这里动态显示 -->
                    </div>
                    <small style="display: block; color: #666; font-size: 12px; margin-top: 5px;">
                        这些知识点将与题目一起保存到题库中
                    </small>
                </div>

                <small style="display: block; color: #666; font-size: 12px;">
                    请确保输入的ID在系统中存在
                </small>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-modal-cancel" onclick="closeSaveToBankModal()">取消</button>
                <button type="button" class="btn-modal-confirm" onclick="confirmSaveToBank()">确定</button>
            </div>
        </div>
    </div>

    <script>
        class QuestionGenerator {
            constructor() {
                this.eventSource = null;
                this.isGenerating = false;
                this.questions = [];
                this.initializeElements();
                this.bindEvents();
            }

            initializeElements() {
                this.form = document.getElementById('questionForm');
                this.generateBtn = document.getElementById('generateBtn');
                this.cancelBtn = document.getElementById('cancelBtn');
                this.generateText = document.getElementById('generateText');
                this.progressContainer = document.getElementById('progressContainer');
                this.progressFill = document.getElementById('progressFill');
                this.progressText = document.getElementById('progressText');
                this.statusMessage = document.getElementById('statusMessage');
                this.questionsList = document.getElementById('questionsList');
                this.statisticsPanel = document.getElementById('statisticsPanel');
                this.statisticsContent = document.getElementById('statisticsContent');

                // Tab相关元素
                this.modeTabs = document.querySelectorAll('.mode-tab');
                this.textModeContent = document.getElementById('textModeContent');
                this.chapterModeContent = document.getElementById('chapterModeContent');
                this.documentModeContent = document.getElementById('documentModeContent');
                this.knowledgeModeContent = document.getElementById('knowledgeModeContent');
                this.currentMode = 'text'; // 默认为文本出题
            }

            bindEvents() {
                this.form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.startGeneration();
                });

                this.cancelBtn.addEventListener('click', () => {
                    this.cancelGeneration();
                });

                // Tab切换事件
                this.modeTabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        this.switchMode(tab.dataset.mode);
                    });
                });
            }

            switchMode(mode) {
                // 更新当前模式
                this.currentMode = mode;

                // 更新tab样式
                this.modeTabs.forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.mode === mode);
                });

                // 隐藏所有内容区域
                this.textModeContent.style.display = 'none';
                this.chapterModeContent.style.display = 'none';
                this.documentModeContent.style.display = 'none';
                this.knowledgeModeContent.style.display = 'none';

                // 重置所有必填字段
                document.getElementById('textContent').required = false;
                document.getElementById('chapterName').required = false;
                document.getElementById('fileUrl').required = false;

                // 切换内容显示并设置必填字段
                if (mode === 'text') {
                    this.textModeContent.style.display = 'block';
                    document.getElementById('textContent').required = true;
                } else if (mode === 'chapter') {
                    this.chapterModeContent.style.display = 'block';
                    // 章节出题支持新旧两种格式，在validateForm中处理验证
                } else if (mode === 'document') {
                    this.documentModeContent.style.display = 'block';
                    // 文档出题支持新旧两种格式，在validateForm中处理验证
                } else if (mode === 'knowledge') {
                    this.knowledgeModeContent.style.display = 'block';
                    // 知识点出题需要验证是否选择了知识点，在validateForm中处理
                }
            }

            startGeneration() {
                if (this.isGenerating) return;

                // 验证表单
                if (!this.validateForm()) return;

                // 重置状态
                this.resetState();
                this.isGenerating = true;
                this.updateUI(true);

                // 构建请求参数
                const params = this.buildRequestParams();

                // 发起SSE请求
                this.connectSSE(params);
            }

            validateForm() {
                const questionTypes = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'));
                if (questionTypes.length === 0) {
                    this.showStatus('请至少选择一种题型！', 'error');
                    return false;
                }

                // 根据当前模式验证不同的字段
                if (this.currentMode === 'text') {
                    const textContent = document.getElementById('textContent').value.trim();
                    if (!textContent) {
                        this.showStatus('请输入出题范围文本！', 'error');
                        return false;
                    }

                    if (textContent.length > 500) {
                        this.showStatus('文本内容不能超过500字符！', 'error');
                        return false;
                    }
                } else if (this.currentMode === 'chapter') {
                    // 检查新格式的章节ID列表
                    const chapterIds = document.getElementById('chapterIds').value.trim();
                    const chapterName = document.getElementById('chapterName').value.trim();

                    if (!chapterIds && !chapterName) {
                        this.showStatus('请输入章节ID列表或章节名称！', 'error');
                        return false;
                    }

                    // 如果使用新格式，验证章节ID格式
                    if (chapterIds) {
                        const chapterIdList = chapterIds.split('\n').map(id => id.trim()).filter(id => id);
                        if (chapterIdList.length === 0) {
                            this.showStatus('请输入有效的章节ID！', 'error');
                            return false;
                        }
                    }

                    // 验证章节出题的补充内容长度
                    const additionalRequirements = document.getElementById('additionalRequirements').value.trim();
                    if (additionalRequirements && additionalRequirements.length > 300) {
                        this.showStatus('补充内容不能超过300字符！', 'error');
                        return false;
                    }
                } else if (this.currentMode === 'document') {
                    // 检查新格式的文档URL列表
                    const fileUrls = document.getElementById('fileUrls').value.trim();
                    const fileUrl = document.getElementById('fileUrl').value.trim();

                    if (!fileUrls && !fileUrl) {
                        this.showStatus('请输入文档URL列表或单个文档URL！', 'error');
                        return false;
                    }

                    // 如果使用新格式，验证URL格式和数量
                    if (fileUrls) {
                        const fileUrlList = fileUrls.split('\n').map(url => url.trim()).filter(url => url);
                        if (fileUrlList.length === 0) {
                            this.showStatus('请输入有效的文档URL！', 'error');
                            return false;
                        }

                        if (fileUrlList.length > 3) {
                            this.showStatus('最多支持3个文档文件！', 'error');
                            return false;
                        }

                        // 验证每个URL格式
                        for (const url of fileUrlList) {
                            try {
                                new URL(url);
                            } catch (e) {
                                this.showStatus(`无效的文档URL: ${url}`, 'error');
                                return false;
                            }
                        }
                    } else if (fileUrl) {
                        // 验证单个URL格式
                        try {
                            new URL(fileUrl);
                        } catch (e) {
                            this.showStatus('请输入有效的文档URL！', 'error');
                            return false;
                        }
                    }

                    // 验证文档出题的补充要求长度
                    const documentRequirements = document.getElementById('documentRequirements').value.trim();
                    if (documentRequirements && documentRequirements.length > 300) {
                        this.showStatus('补充要求不能超过300字符！', 'error');
                        return false;
                    }
                } else if (this.currentMode === 'knowledge') {
                    // 验证是否选择了知识点
                    const selectedKnowledgePoints = Array.from(document.querySelectorAll('input[name="knowledgePoint"]:checked'));
                    if (selectedKnowledgePoints.length === 0) {
                        this.showStatus('请至少选择一个知识点！', 'error');
                        return false;
                    }

                    // 验证知识点出题的补充要求长度
                    const knowledgeRequirements = document.getElementById('knowledgeRequirements').value.trim();
                    if (knowledgeRequirements && knowledgeRequirements.length > 300) {
                        this.showStatus('补充要求不能超过300字符！', 'error');
                        return false;
                    }
                }

                const authToken = document.getElementById('authToken').value.trim();
                if (!authToken) {
                    this.showStatus('请输入认证Token！', 'error');
                    return false;
                }

                return true;
            }

            buildRequestParams() {
                const questionTypeIds = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                    .map(cb => parseInt(cb.value));

                // 获取难度和方向的文本值（支持空值）
                const difficultySelect = document.getElementById('difficulty');
                const directionSelect = document.getElementById('direction');

                // 如果没有选择，则传空字符串，后端会使用默认值
                const difficultyLevelName = difficultySelect.value ? difficultySelect.options[difficultySelect.selectedIndex].text : '';
                const questionDirectionName = directionSelect.value ? directionSelect.options[directionSelect.selectedIndex].text : '';

                // 基础参数
                const baseParams = {
                    questionTypeIds: questionTypeIds,
                    difficultyLevelName: difficultyLevelName,
                    questionDirectionName: questionDirectionName,
                    questionCount: parseInt(document.getElementById('questionCount').value),
                    aiModeId: document.getElementById('aiModeId').value.trim(),
                    grade: 1 // 默认年级为1
                };

                // 根据当前模式添加特定参数
                if (this.currentMode === 'text') {
                    return {
                        ...baseParams,
                        mode: 2, // 文本出题
                        textContent: document.getElementById('textContent').value.trim()
                    };
                } else if (this.currentMode === 'chapter') {
                    // 获取章节出题的补充内容
                    const additionalRequirements = document.getElementById('additionalRequirements').value.trim();
                    const chapterIds = document.getElementById('chapterIds').value.trim();
                    const chapterName = document.getElementById('chapterName').value.trim();

                    const params = {
                        ...baseParams,
                        mode: 4 // 章节出题
                    };

                    // 优先使用新格式的章节ID列表
                    if (chapterIds) {
                        const chapterIdList = chapterIds.split('\n').map(id => id.trim()).filter(id => id);
                        params.chapterIds = chapterIdList;
                    } else if (chapterName) {
                        // 兼容旧格式的章节名称
                        params.chapterName = chapterName;
                    }

                    if (additionalRequirements) {
                        params.additionalRequirements = additionalRequirements;
                    }
                    return params;
                } else if (this.currentMode === 'document') {
                    // 获取文档出题的补充要求
                    const documentRequirements = document.getElementById('documentRequirements').value.trim();
                    const fileUrls = document.getElementById('fileUrls').value.trim();
                    const fileUrl = document.getElementById('fileUrl').value.trim();

                    const params = {
                        ...baseParams,
                        mode: 3 // 文档出题 (Attachment)
                    };

                    // 优先使用新格式的文档URL列表
                    if (fileUrls) {
                        const fileUrlList = fileUrls.split('\n').map(url => url.trim()).filter(url => url);
                        params.fileUrls = fileUrlList;
                    } else if (fileUrl) {
                        // 兼容旧格式的单个文档URL
                        params.fileUrl = fileUrl;
                    }

                    if (documentRequirements) {
                        params.additionalRequirements = documentRequirements;
                    }
                    return params;
                } else if (this.currentMode === 'knowledge') {
                    // 获取选中的知识点
                    const selectedKnowledgePoints = Array.from(document.querySelectorAll('input[name="knowledgePoint"]:checked'))
                        .map(cb => cb.value);

                    // 获取知识点出题的补充要求
                    const knowledgeRequirements = document.getElementById('knowledgeRequirements').value.trim();
                    const params = {
                        ...baseParams,
                        mode: 1, // 知识点出题 (KnowledgePoint)
                        knowledgePointIds: selectedKnowledgePoints
                    };

                    if (knowledgeRequirements) {
                        params.additionalRequirements = knowledgeRequirements;
                    }
                    return params;
                }

                return baseParams;
            }

            connectSSE(params) {
                try {
                    // 构建URL和请求体
                    const url = 'https://agentapi.test.eduwon.cn/AgentIntelligentQuestion/AgentIntelligentQuestion/GenerateQuestionsStream';

                    // 获取认证Token
                    const authToken = document.getElementById('authToken').value.trim();

                    // 使用fetch发起POST请求
                    fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'text/event-stream',
                            'Authorization': authToken
                        },
                        body: JSON.stringify(params)
                    }).then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const reader = response.body.getReader();
                        const decoder = new TextDecoder();

                        const readStream = () => {
                            reader.read().then(({ done, value }) => {
                                if (done) {
                                    this.handleComplete();
                                    return;
                                }

                                const chunk = decoder.decode(value, { stream: true });
                                const lines = chunk.split('\n');

                                for (const line of lines) {
                                    if (line.startsWith('data: ')) {
                                        try {
                                            const data = JSON.parse(line.substring(6));
                                            this.handleSSEMessage(data);
                                        } catch (e) {
                                            console.warn('解析SSE数据失败:', e, line);
                                        }
                                    }
                                }

                                if (this.isGenerating) {
                                    readStream();
                                }
                            }).catch(error => {
                                this.handleError('连接中断: ' + error.message);
                            });
                        };

                        readStream();
                    }).catch(error => {
                        this.handleError('请求失败: ' + error.message);
                    });

                } catch (error) {
                    this.handleError('连接失败: ' + error.message);
                }
            }

            handleSSEMessage(data) {
                console.log('收到SSE消息:', data);

                switch (data.MessageType) {
                    case 'progress':
                        this.handleProgress(data.Progress);
                        break;
                    case 'question':
                        this.handleQuestion(data.Question, data.Progress);
                        break;
                    case 'statistics':
                        this.handleStatistics(data.StatisticsText);
                        break;
                    case 'complete':
                        this.handleComplete(data.Content);
                        break;
                    case 'error':
                        this.handleError(data.Content);
                        break;
                }
            }

            handleProgress(progress) {
                if (progress) {
                    const percentage = progress.Total > 0 ? (progress.Current / progress.Total) * 100 : 0;
                    this.progressFill.style.width = percentage + '%';

                    // 优化进度文本显示
                    let progressText = progress.Description || '处理中...';
                    if (progress.Current > 0 && progress.Total > 0) {
                        progressText = `${progressText} (${progress.Current}/${progress.Total})`;
                    }

                    this.progressText.textContent = progressText;

                    // 如果是AI生成进度，添加特殊样式
                    if (progressText.includes('正在生成题目') || progressText.includes('已接收')) {
                        this.progressFill.style.background = 'linear-gradient(90deg, #4facfe 0%, #00f2fe 100%, #ff6b6b 100%)';
                        this.progressText.style.color = '#4facfe';
                    } else {
                        this.progressFill.style.background = 'linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)';
                        this.progressText.style.color = '#666';
                    }
                }
            }

            handleQuestion(question, progress) {
                if (question) {
                    console.log('接收到题目数据:', question);
                    console.log('题目Title字段:', question.Title);
                    console.log('题目QuestionType字段:', question.QuestionType);

                    this.questions.push(question);

                    // 添加流式生成的视觉效果
                    this.addQuestionToUIWithAnimation(question, this.questions.length);

                    // 更新实时统计
                    this.updateRealTimeStatistics();

                    if (progress) {
                        this.handleProgress(progress);
                    }
                }
            }

            handleStatistics(statisticsText) {
                if (statisticsText) {
                    this.statisticsContent.textContent = statisticsText;
                    this.statisticsPanel.style.display = 'block';
                }
            }

            handleComplete(message) {
                this.isGenerating = false;
                this.updateUI(false);
                this.showStatus(message || `成功生成${this.questions.length}道题目！`, 'success');
                this.progressContainer.style.display = 'none';

                // 添加批量保存按钮
                if (this.questions.length > 0) {
                    // 先移除已存在的批量保存按钮
                    const existingBtn = this.questionsList.querySelector('.btn-save-all-to-bank');
                    if (existingBtn) {
                        existingBtn.remove();
                    }

                    const batchSaveBtn = document.createElement('button');
                    batchSaveBtn.className = 'btn-save-all-to-bank';
                    batchSaveBtn.innerHTML = '📚 批量加入题库';
                    batchSaveBtn.onclick = () => saveBatchQuestionsToBank();
                    this.questionsList.appendChild(batchSaveBtn);
                }
            }

            handleError(message) {
                this.isGenerating = false;
                this.updateUI(false);
                this.showStatus('生成失败: ' + message, 'error');
                this.progressContainer.style.display = 'none';
            }

            addQuestionToUI(question, questionNumber) {
                const questionElement = document.createElement('div');
                questionElement.className = 'question-item';
                questionElement.innerHTML = this.buildQuestionHTML(question, questionNumber);
                this.questionsList.appendChild(questionElement);
            }

            addQuestionToUIWithAnimation(question, questionNumber) {
                // 如果是第一个题目，移除流式提示
                if (questionNumber === 1) {
                    removeStreamingHint();
                }

                const questionElement = document.createElement('div');
                questionElement.className = 'question-item';
                questionElement.style.opacity = '0';
                questionElement.style.transform = 'translateY(20px)';
                questionElement.innerHTML = this.buildQuestionHTML(question, questionNumber);

                // 添加流式生成标识
                const streamingBadge = document.createElement('div');
                streamingBadge.className = 'streaming-badge';
                streamingBadge.innerHTML = '✨ 实时生成';
                streamingBadge.style.cssText = `
                    position: absolute;
                    top: -10px;
                    right: -10px;
                    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 10px;
                    font-weight: 600;
                    animation: pulse 2s infinite;
                    z-index: 10;
                `;

                questionElement.style.position = 'relative';
                questionElement.appendChild(streamingBadge);

                this.questionsList.appendChild(questionElement);

                // 触发动画
                setTimeout(() => {
                    questionElement.style.transition = 'all 0.5s ease';
                    questionElement.style.opacity = '1';
                    questionElement.style.transform = 'translateY(0)';
                }, 50);

                // 3秒后移除流式标识
                setTimeout(() => {
                    if (streamingBadge.parentNode) {
                        streamingBadge.style.animation = 'fadeOut 0.5s ease';
                        setTimeout(() => {
                            if (streamingBadge.parentNode) {
                                streamingBadge.remove();
                            }
                        }, 500);
                    }
                }, 3000);
            }

            updateRealTimeStatistics() {
                // 更新实时统计信息
                const typeCount = {};
                this.questions.forEach(q => {
                    const type = q.QuestionType || '未知题型';
                    typeCount[type] = (typeCount[type] || 0) + 1;
                });

                const statsText = `实时统计：已生成 ${this.questions.length} 道题目\n` +
                    Object.entries(typeCount).map(([type, count]) => `${type}: ${count}道`).join('，');

                // 如果统计面板存在，更新内容
                if (this.statisticsPanel.style.display === 'block') {
                    this.statisticsContent.textContent = statsText;
                } else {
                    // 显示统计面板
                    this.statisticsContent.textContent = statsText;
                    this.statisticsPanel.style.display = 'block';
                }
            }

            buildQuestionHTML(question, questionNumber) {
                let optionsHTML = '';
                if (question.Options && question.Options.length > 0) {
                    optionsHTML = '<div class="question-options">';
                    question.Options.forEach(option => {
                        const isCorrect = question.Answer && question.Answer.includes(option.Option);
                        optionsHTML += `
                            <div class="option-item ${isCorrect ? 'correct' : ''}">
                                <strong>${option.Option}.</strong> ${option.Content}
                            </div>
                        `;
                    });
                    optionsHTML += '</div>';
                }

                // 构建知识点信息HTML
                let knowledgePointsHTML = '';
                if (question.KnowledgePoints && question.KnowledgePoints.length > 0) {
                    knowledgePointsHTML = '<div class="question-knowledge-points">';
                    knowledgePointsHTML += '<strong>🧠 关联知识点：</strong>';
                    knowledgePointsHTML += '<div class="knowledge-points-list">';
                    question.KnowledgePoints.forEach(kp => {
                        knowledgePointsHTML += `
                            <span class="knowledge-point-tag level-${kp.Level}">
                                ${kp.Content} <small>(L${kp.Level})</small>
                            </span>
                        `;
                    });
                    knowledgePointsHTML += '</div>';
                    knowledgePointsHTML += '</div>';
                }

                // 构建章节信息HTML
                let chaptersHTML = '';
                if (question.Chapters && question.Chapters.length > 0) {
                    chaptersHTML = '<div class="question-knowledge-points" style="border-left-color: #28a745;">';
                    chaptersHTML += '<strong>📚 关联章节：</strong>';
                    chaptersHTML += '<div class="knowledge-points-list">';
                    question.Chapters.forEach(chapter => {
                        chaptersHTML += `
                            <span class="knowledge-point-tag" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                ${chapter.ChapterName} <small>(${chapter.Id})</small>
                            </span>
                        `;
                    });
                    chaptersHTML += '</div>';
                    chaptersHTML += '</div>';
                }

                return `
                    <div class="question-header">
                        <div class="question-number">第 ${questionNumber} 题</div>
                        <div class="question-type">${question.QuestionType || '未知题型'}</div>
                    </div>
                    <div class="question-title">${question.Title || '题目内容'}</div>
                    ${optionsHTML}
                    ${knowledgePointsHTML}
                    ${chaptersHTML}
                    <div class="question-answer">
                        <strong>正确答案：</strong>${question.Answer || '无'}
                    </div>
                    <div class="question-analysis">
                        <strong>答案解析：</strong>${question.Analysis || '暂无解析'}
                    </div>
                    <div class="question-actions">
                        <button class="btn-regenerate" onclick="regenerateQuestion(${questionNumber - 1})">
                            🔄 换一题
                        </button>
                        <button class="btn-save-to-bank" onclick="saveSingleQuestionToBank(${questionNumber - 1})">
                            💾 加入题库
                        </button>
                    </div>
                `;
            }

            cancelGeneration() {
                this.isGenerating = false;
                this.updateUI(false);
                this.showStatus('题目生成已取消', 'info');
                this.progressContainer.style.display = 'none';
            }

            resetState() {
                this.questions = [];
                this.questionsList.innerHTML = '';
                this.statusMessage.style.display = 'none';
                this.statisticsPanel.style.display = 'none';
                this.progressContainer.style.display = 'block';
                this.progressFill.style.width = '0%';
                this.progressText.textContent = '🚀 启动流式AI生成...';

                // 添加流式生成提示
                addStreamingHint();
            }

            updateUI(isGenerating) {
                this.generateBtn.disabled = isGenerating;
                this.cancelBtn.style.display = isGenerating ? 'block' : 'none';
                
                if (isGenerating) {
                    this.generateText.innerHTML = '<div class="loading"></div>正在生成题目...';
                } else {
                    this.generateText.textContent = '🚀 开始生成题目';
                }
            }

            showStatus(message, type) {
                this.statusMessage.textContent = message;
                this.statusMessage.className = `status-message status-${type}`;
                this.statusMessage.style.display = 'block';
                
                // 3秒后自动隐藏成功消息
                if (type === 'success') {
                    setTimeout(() => {
                        this.statusMessage.style.display = 'none';
                    }, 3000);
                }
            }
        }

        // 全局变量
        let questionGenerator = null;
        let currentRegenerateIndex = -1;
        let currentSaveIndex = -1;
        let isBatchSave = false;

        // 换一题功能
        function regenerateQuestion(questionIndex) {
            console.log('regenerateQuestion called with index:', questionIndex);
            console.log('questionGenerator:', questionGenerator);
            console.log('questions array:', questionGenerator?.questions);

            if (!questionGenerator || !questionGenerator.questions[questionIndex]) {
                alert('题目信息不存在！');
                return;
            }

            currentRegenerateIndex = questionIndex;
            document.getElementById('userRequirement').value = '';
            document.getElementById('regenerateModal').style.display = 'block';
        }

        function closeRegenerateModal() {
            document.getElementById('regenerateModal').style.display = 'none';
           // currentRegenerateIndex = -1;
        }

        async function confirmRegenerate() {
            if (currentRegenerateIndex === -1) return;

            const userRequirement = document.getElementById('userRequirement').value.trim();
            const originalQuestion = questionGenerator.questions[currentRegenerateIndex];
            const authToken = document.getElementById('authToken').value.trim();
            const aiModeId = document.getElementById('aiModeId').value.trim();

            if (!authToken) {
                alert('请输入认证Token！');
                return;
            }

            if (!aiModeId) {
                alert('请输入AI模型ID！');
                return;
            }

            // 关闭模态框
            closeRegenerateModal();

            // 获取题目元素和按钮
            const questionElements = document.querySelectorAll('.question-item');
            if (currentRegenerateIndex >= questionElements.length) {
                alert('题目索引错误！');
                return;
            }

            const questionElement = questionElements[currentRegenerateIndex];
            const regenerateBtn = questionElement.querySelector('.btn-regenerate');

            if (!regenerateBtn) {
                alert('找不到换一题按钮！');
                return;
            }

            // 保存原始按钮文本并显示加载状态
            const originalText = regenerateBtn.innerHTML;
            regenerateBtn.disabled = true;
            regenerateBtn.innerHTML = '<div class="loading"></div>正在重新生成...';

            try {
                const response = await fetch('https://agentapi.test.eduwon.cn/AgentIntelligentQuestion/AgentIntelligentQuestion/RegenerateQuestion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authToken
                    },
                    body: JSON.stringify({
                        AIModeId: aiModeId,
                        OriginalQuestion: originalQuestion,
                        UserRequirement: userRequirement || null
                    })
                });

                const result = await response.json();

                if (result.Data && result.Data.code === 200) {
                    // 更新题目数据
                    questionGenerator.questions[currentRegenerateIndex] = result.Data.data;

                    // 更新UI
                    questionElement.innerHTML = questionGenerator.buildQuestionHTML(result.Data.data, currentRegenerateIndex + 1);

                    // 显示成功消息
                    questionGenerator.showStatus('题目重新生成成功！', 'success');
                } else {
                    throw new Error(result.message || '重新生成失败');
                }
            } catch (error) {
                console.error('重新生成题目失败:', error);
                questionGenerator.showStatus('重新生成失败: ' + error.message, 'error');

                // 恢复按钮状态
                if (regenerateBtn) {
                    regenerateBtn.disabled = false;
                    regenerateBtn.innerHTML = originalText;
                }
            }
            finally{
                currentRegenerateIndex = -1;
            }
        }

        // 保存单题到题库功能
        function saveSingleQuestionToBank(questionIndex) {
            if (!questionGenerator || !questionGenerator.questions[questionIndex]) {
                alert('题目信息不存在！');
                return;
            }

            currentSaveIndex = questionIndex;
            isBatchSave = false;

            // 清空表单
            document.getElementById('chapterId').value = '1C5F628F-366C-453B-8444-C8228995C295';
            document.getElementById('difficultyId').value = '22F280C9-0FA3-49EB-9F56-A794E3B02C5B';
            document.getElementById('learningLevelId').value = 'E4FE5363-4186-4654-A823-16DDB63B1E94';

            // 显示知识点信息
            displayKnowledgePointsInModal([questionGenerator.questions[questionIndex]]);

            document.getElementById('saveToBankModal').style.display = 'block';
        }

        // 批量保存到题库功能
        function saveBatchQuestionsToBank() {
            if (!questionGenerator || !questionGenerator.questions || questionGenerator.questions.length === 0) {
                alert('没有题目可以保存！');
                return;
            }

            currentSaveIndex = -1;
            isBatchSave = true;

            // 清空表单
            document.getElementById('chapterId').value = '1C5F628F-366C-453B-8444-C8228995C295';
            document.getElementById('difficultyId').value = '22F280C9-0FA3-49EB-9F56-A794E3B02C5B';
            document.getElementById('learningLevelId').value = 'E4FE5363-4186-4654-A823-16DDB63B1E94';

            // 显示知识点信息
            displayKnowledgePointsInModal(questionGenerator.questions);

            document.getElementById('saveToBankModal').style.display = 'block';
        }

        // 在保存模态框中显示知识点信息
        function displayKnowledgePointsInModal(questions) {
            const knowledgePointsInfo = document.getElementById('knowledgePointsInfo');
            const knowledgePointsList = document.getElementById('knowledgePointsList');

            // 收集所有题目的知识点
            const allKnowledgePoints = new Map(); // 使用Map去重

            questions.forEach((question, index) => {
                if (question.KnowledgePoints && question.KnowledgePoints.length > 0) {
                    question.KnowledgePoints.forEach(kp => {
                        if (kp.Id && kp.Content) {
                            allKnowledgePoints.set(kp.Id, {
                                id: kp.Id,
                                content: kp.Content,
                                level: kp.Level || 1,
                                questionIndexes: allKnowledgePoints.has(kp.Id) ?
                                    [...allKnowledgePoints.get(kp.Id).questionIndexes, index + 1] :
                                    [index + 1]
                            });
                        }
                    });
                }
            });

            if (allKnowledgePoints.size > 0) {
                // 显示知识点信息
                knowledgePointsInfo.style.display = 'block';

                let html = '';
                allKnowledgePoints.forEach((kp, id) => {
                    const questionText = questions.length === 1 ? '' : ` (第${kp.questionIndexes.join('、')}题)`;
                    html += `
                        <div style="display: flex; align-items: center; margin-bottom: 8px; padding: 6px; background: white; border-radius: 4px; border-left: 3px solid #007bff;">
                            <span class="knowledge-point-tag level-${kp.level}" style="margin-right: 10px;">
                                ${kp.content} <small>(L${kp.level})</small>
                            </span>
                            <small style="color: #666; font-size: 11px;">ID: ${kp.id}${questionText}</small>
                        </div>
                    `;
                });

                knowledgePointsList.innerHTML = html;
            } else {
                // 隐藏知识点信息
                knowledgePointsInfo.style.display = 'none';
            }
        }

        // 统计题目中的知识点数量
        function getKnowledgePointsCount(questions) {
            const allKnowledgePoints = new Set(); // 使用Set去重

            questions.forEach(question => {
                if (question.KnowledgePoints && question.KnowledgePoints.length > 0) {
                    question.KnowledgePoints.forEach(kp => {
                        if (kp.Id) {
                            allKnowledgePoints.add(kp.Id);
                        }
                    });
                }
            });

            return allKnowledgePoints.size;
        }

        // 关闭保存到题库模态框
        function closeSaveToBankModal() {
            document.getElementById('saveToBankModal').style.display = 'none';
            // 隐藏知识点信息
            document.getElementById('knowledgePointsInfo').style.display = 'none';
            //currentSaveIndex = -1;
            //isBatchSave = false;
        }

        // 确认保存到题库
        async function confirmSaveToBank() {
            const chapterId = document.getElementById('chapterId').value.trim();
            const difficultyId = document.getElementById('difficultyId').value.trim();
            const learningLevelId = document.getElementById('learningLevelId').value.trim();
            const authToken = document.getElementById('authToken').value.trim();

            // 参数验证
            if (!authToken) {
                alert('请输入认证Token！');
                return;
            }

            if (!chapterId) {
                alert('请输入章节ID！');
                return;
            }

            if (!difficultyId) {
                alert('请输入难度ID！');
                return;
            }

            if (!learningLevelId) {
                alert('请输入学习水平ID！');
                return;
            }

            // 关闭模态框
            closeSaveToBankModal();

            try {
                let result;
                if (isBatchSave) {
                    // 批量保存
                    console.log('批量保存的题目数据:', questionGenerator.questions);
                    console.log('第一道题目Title:', questionGenerator.questions[0]?.Title);

                    const requestData = {
                        Questions: questionGenerator.questions,
                        ChapterId: chapterId,
                        DifficultyId: difficultyId,
                        LearningLevelId: learningLevelId
                    };

                    console.log('批量保存请求数据:', requestData);

                    const response = await fetch('https://agentapi.test.eduwon.cn/AgentIntelligentQuestion/AgentIntelligentQuestion/SaveBatchQuestionsToBank', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': authToken
                        },
                        body: JSON.stringify(requestData)
                    });

                    result = await response.json();

                     if (result.Data && result.Data.code === 200) {
                        // 统计知识点信息
                        const knowledgePointsCount = getKnowledgePointsCount(questionGenerator.questions);
                        const knowledgePointsText = knowledgePointsCount > 0 ? `，关联知识点${knowledgePointsCount}个` : '';
                        questionGenerator.showStatus(`批量保存成功！成功${result.Data.data.SuccessCount}个，失败${result.Data.data.FailedCount}个${knowledgePointsText}`, 'success');
                    } else {
                        throw new Error(result.message || '批量保存失败');
                    }
                } else {
                    // 单题保存
                    const question = questionGenerator.questions[currentSaveIndex];

                    // 调试：打印题目数据
                    console.log('保存的题目数据:', question);
                    console.log('题目Title:', question.Title);
                    console.log('题目QuestionType:', question.QuestionType);

                    const requestData = {
                        Question: question,
                        ChapterId: chapterId,
                        DifficultyId: difficultyId,
                        LearningLevelId: learningLevelId
                    };

                    console.log('发送的请求数据:', requestData);

                    const response = await fetch('https://agentapi.test.eduwon.cn/AgentIntelligentQuestion/AgentIntelligentQuestion/SaveSingleQuestionToBank', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': authToken
                        },
                        body: JSON.stringify(requestData)
                    });

                    result = await response.json();

                    if (result.Data && result.Data.code === 200) {
                        // 统计知识点信息
                        const knowledgePointsCount = getKnowledgePointsCount([question]);
                        const knowledgePointsText = knowledgePointsCount > 0 ? `，关联知识点${knowledgePointsCount}个` : '';
                        questionGenerator.showStatus(`题目保存成功！${knowledgePointsText}`, 'success');
                    } else {
                        throw new Error(result.message || '题目保存失败');
                    }
                }
            } catch (error) {
                console.error('保存到题库失败:', error);
                questionGenerator.showStatus('保存失败: ' + error.message, 'error');
            }
            finally{
                isBatchSave = false;
                currentSaveIndex = -1;
                console.log('保存操作完成');
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const regenerateModal = document.getElementById('regenerateModal');
            const saveToBankModal = document.getElementById('saveToBankModal');

            if (event.target === regenerateModal) {
                closeRegenerateModal();
            } else if (event.target === saveToBankModal) {
                closeSaveToBankModal();
            }
        }

        // 加载知识点列表
        async function loadKnowledgePoints() {
            const subjectId = document.getElementById('subjectId').value;
            const container = document.getElementById('knowledgePointsContainer');

            if (!subjectId) {
                container.innerHTML = '<div style="color: #999; text-align: center; padding: 20px;">请先选择学科</div>';
                return;
            }

            // 显示加载状态
            container.innerHTML = '<div style="color: #666; text-align: center; padding: 20px;">正在加载知识点...</div>';

            try {
                // 获取认证Token
                const authToken = document.getElementById('authToken').value.trim();
                if (!authToken) {
                    container.innerHTML = '<div style="color: #f56565; text-align: center; padding: 20px;">请先输入认证Token</div>';
                    return;
                }

                // 调用API获取知识点
                const response = await fetch(`https://agentapi.test.eduwon.cn/AgentIntelligentQuestion/AgentIntelligentQuestion/GetKnowledgePointsBySubject?subjectId=${subjectId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': authToken,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const responseData = await response.json();
                console.log('API返回数据:', responseData); // 调试日志

                // 处理不同的响应格式
                let knowledgePoints;
                if (Array.isArray(responseData)) {
                    knowledgePoints = responseData;
                } else if (responseData && responseData.Data && Array.isArray(responseData.Data)) {
                    knowledgePoints = responseData.Data;
                } else if (responseData && Array.isArray(responseData.result)) {
                    knowledgePoints = responseData.result;
                } else {
                    console.error('无法解析知识点数据:', responseData);
                    container.innerHTML = '<div style="color: #f56565; text-align: center; padding: 20px;">数据格式错误，请检查API返回格式</div>';
                    return;
                }

                if (!knowledgePoints || knowledgePoints.length === 0) {
                    container.innerHTML = '<div style="color: #999; text-align: center; padding: 20px;">该学科暂无知识点数据</div>';
                    return;
                }

                // 渲染知识点树形列表
                let html = '';
                html += renderKnowledgePointTree(knowledgePoints, 0);
                container.innerHTML = html;

            } catch (error) {
                console.error('加载知识点失败:', error);
                container.innerHTML = `<div style="color: #f56565; text-align: center; padding: 20px;">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染知识点树形结构
        function renderKnowledgePointTree(points, level) {
            let html = '';

            // 检查数据类型
            if (!Array.isArray(points)) {
                console.error('renderKnowledgePointTree: points不是数组', points);
                return '<div style="color: #f56565; padding: 10px;">数据格式错误</div>';
            }

            points.forEach(point => {
                const indent = level * 20; // 每层缩进20px
                const hasChildren = point.Children && point.Children.length > 0;
                const expandIcon = hasChildren ? (point.Expanded ? '▼' : '▶') : '•';
                const expandClass = hasChildren ? 'expandable' : '';

                html += `
                    <div style="margin-left: ${indent}px; margin-bottom: 4px;">
                        <label style="display: flex; align-items: center; cursor: pointer; padding: 4px; border-radius: 3px; transition: background-color 0.2s;"
                               onmouseover="this.style.backgroundColor='#f0f0f0'"
                               onmouseout="this.style.backgroundColor='transparent'">
                            ${hasChildren ?
                                `<span class="expand-icon ${expandClass}" onclick="toggleKnowledgePoint(event, '${point.Id}')"
                                      style="margin-right: 5px; cursor: pointer; user-select: none; width: 12px; font-size: 12px;">${expandIcon}</span>` :
                                `<span style="margin-right: 5px; width: 12px; font-size: 12px; color: #ccc;">${expandIcon}</span>`
                            }
                            <input type="checkbox" name="knowledgePoint" value="${point.Id}" style="margin-right: 8px;">
                            <span style="font-size: 14px; color: ${level === 0 ? '#333' : level === 1 ? '#666' : '#999'}; font-weight: ${level === 0 ? '600' : 'normal'};">
                                ${point.Content}
                            </span>
                            <span style="color: #999; font-size: 11px; margin-left: 5px;">(L${point.Level})</span>
                        </label>
                        ${hasChildren ?
                            `<div id="children-${point.Id}" style="display: ${point.Expanded ? 'block' : 'none'};">
                                ${renderKnowledgePointTree(point.Children, level + 1)}
                            </div>` : ''
                        }
                    </div>
                `;
            });

            return html;
        }

        function addStreamingHint() {
            // 在题目列表区域添加流式生成提示
            const questionsList = document.getElementById('questionsList');
            const hintElement = document.createElement('div');
            hintElement.className = 'streaming-hint';
            hintElement.innerHTML = `
                <div style="text-align: center; padding: 40px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white; margin-bottom: 20px;">
                    <div style="font-size: 2.5em; margin-bottom: 15px;">⚡</div>
                    <h3 style="margin: 0 0 10px 0; font-size: 1.2em;">流式AI生成中</h3>
                    <p style="margin: 0; opacity: 0.9; font-size: 0.9em;">题目将实时显示，无需等待全部生成完成</p>
                    <div style="margin-top: 15px;">
                        <div class="streaming-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            `;

            questionsList.appendChild(hintElement);

            // 添加动态点点点动画的CSS
            if (!document.getElementById('streaming-dots-style')) {
                const style = document.createElement('style');
                style.id = 'streaming-dots-style';
                style.textContent = `
                    .streaming-dots {
                        display: inline-flex;
                        gap: 4px;
                    }
                    .streaming-dots span {
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background: white;
                        animation: streamingDots 1.4s infinite ease-in-out both;
                    }
                    .streaming-dots span:nth-child(1) { animation-delay: -0.32s; }
                    .streaming-dots span:nth-child(2) { animation-delay: -0.16s; }
                    .streaming-dots span:nth-child(3) { animation-delay: 0s; }

                    @keyframes streamingDots {
                        0%, 80%, 100% {
                            transform: scale(0.8);
                            opacity: 0.5;
                        }
                        40% {
                            transform: scale(1);
                            opacity: 1;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        function removeStreamingHint() {
            const questionsList = document.getElementById('questionsList');
            const hintElement = questionsList.querySelector('.streaming-hint');
            if (hintElement) {
                hintElement.style.transition = 'all 0.5s ease';
                hintElement.style.opacity = '0';
                hintElement.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    if (hintElement.parentNode) {
                        hintElement.remove();
                    }
                }, 500);
            }
        }

        // 切换知识点展开/折叠状态
        function toggleKnowledgePoint(event, pointId) {
            event.stopPropagation(); // 阻止事件冒泡

            const childrenDiv = document.getElementById(`children-${pointId}`);
            const expandIcon = event.target;

            if (childrenDiv.style.display === 'none') {
                childrenDiv.style.display = 'block';
                expandIcon.textContent = '▼';
            } else {
                childrenDiv.style.display = 'none';
                expandIcon.textContent = '▶';
            }
        }

        // 全部展开知识点
        function expandAllKnowledgePoints() {
            const expandIcons = document.querySelectorAll('.expand-icon');
            expandIcons.forEach(icon => {
                const pointId = icon.onclick.toString().match(/'([^']+)'/)[1];
                const childrenDiv = document.getElementById(`children-${pointId}`);
                if (childrenDiv) {
                    childrenDiv.style.display = 'block';
                    icon.textContent = '▼';
                }
            });
        }

        // 全部折叠知识点
        function collapseAllKnowledgePoints() {
            const expandIcons = document.querySelectorAll('.expand-icon');
            expandIcons.forEach(icon => {
                const pointId = icon.onclick.toString().match(/'([^']+)'/)[1];
                const childrenDiv = document.getElementById(`children-${pointId}`);
                if (childrenDiv) {
                    childrenDiv.style.display = 'none';
                    icon.textContent = '▶';
                }
            });
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            questionGenerator = new QuestionGenerator();
        });
    </script>
</body>
</html>
