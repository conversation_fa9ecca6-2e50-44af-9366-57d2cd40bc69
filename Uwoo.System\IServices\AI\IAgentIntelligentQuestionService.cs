using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_智能出题服务接口
    /// </summary>
    public interface IAgentIntelligentQuestionService : IService<AI_AgentBaseInfo>
    {
        /// <summary>
        /// 获取题型列表
        /// </summary>
        /// <returns></returns>
        List<QuestionTypeDto> GetQuestionTypes();

        /// <summary>
        /// 获取难度等级列表
        /// </summary>
        /// <returns></returns>
        List<DifficultyLevelDto> GetDifficultyLevels();

        /// <summary>
        /// 获取出题方向列表
        /// </summary>
        /// <returns></returns>
        List<QuestionDirectionDto> GetQuestionDirections();

        /// <summary>
        /// 根据学科获取知识点列表
        /// </summary>
        /// <param name="subjectId">学科ID</param>
        /// <returns></returns>
        Task<List<KnowledgePointDto>> GetKnowledgePointsBySubject(string subjectId);

        /// <summary>
        /// 智能出题（异步）
        /// </summary>
        /// <param name="input"></param>
        /// <returns>任务ID</returns>
        Task<string> GenerateQuestionsAsync(IntelligentQuestionGenerationInput input);

        /// <summary>
        /// 智能出题（SSE流式返回）
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task GenerateQuestionsStream(IntelligentQuestionGenerationInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 重新生成单题
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<RegenerateQuestionOutput> RegenerateQuestion(RegenerateQuestionInput input);

        /// <summary>
        /// 单题保存到题库
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<SaveToQuestionBankOutput> SaveSingleQuestionToBank(SaveSingleQuestionToBankInput input);

        /// <summary>
        /// 批量保存到题库
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<SaveToQuestionBankOutput> SaveBatchQuestionsToBank(SaveBatchQuestionsToBankInput input);
    }
}
