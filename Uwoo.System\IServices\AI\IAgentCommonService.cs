﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_通用功能
    /// </summary>
    public interface IAgentCommonService : IService<AI_AgentBaseInfo>
    {
        /// <summary>
        /// AI图片地址转华为云OBS
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<List<ImgInput>> AIUrlReplaceHWYOBS(List<ImgInput> input);

        /// <summary>
        /// AI_生成图片
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<string> AIGenerateImage(AIGenerateImageInput input);

        /// <summary>
        /// AI_生成HTML代码
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<AIGenerateHTMLCodeOutput> AIGenerateHTMLCode(AIGenerateHTMLCodeInput input);

        /// <summary>
        /// AI_生成题目
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<AIGenerateQuestionsOutput> AIGenerateQuestions(AIGenerateQuestionsInput input);

        /// <summary>
        /// AI_生成题目（流式版本）
        /// </summary>
        /// <param name="input">输入参数</param>
        /// <param name="onQuestionGenerated">单个题目生成完成时的回调</param>
        /// <param name="onProgress">进度更新回调</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<AIGenerateQuestionsOutput> AIGenerateQuestionsStream(
            AIGenerateQuestionsInput input,
            Func<AIGeneratedQuestion, int, Task> onQuestionGenerated = null,
            Func<string, Task> onProgress = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取AI文件消息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<List<AIFileInfoDto>> GetAIFileInfoList();

        /// <summary>
        /// 创建上下文缓存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<string> CreateContext(CreateContextInput input);

        /// <summary>
        /// 上下文对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task ContextDialogue(ContextDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken = default);

        /// <summary>
        /// AI对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        Task AIDialogue(AIDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 音频文件转文本
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task<string> AudioFileChangeText(AudioFileChangeTextInput input);

        /// <summary>
        /// 文本转语音
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task TextChangeVoice(AITextChangeVoiceInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 获取AI对话内容记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageReturn<GetAIDialogueContentRecordOutput>> GetAIDialogueContentRecord(GetAIDialogueContentRecordInput input);

        /// <summary>
        /// 获取智能体模型列表
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<List<GetAgentModelInfoOutput>> GetAgentModelInfo();

        /// <summary>
        /// 文件分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<FileAnalysisDouBaoOutput>> FileAnalysis(FileAnalysisInput input);
    }
}
