﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_学生端口语交际
    /// </summary>
    public interface IAgentStudentOralCommunicationService : IService<AI_OralCommunicationTask>
    {
        /// <summary>
        /// 智能体_学生端口语交际对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        Task AgentStudentOralCommunicationDialogue(AgentStudentOralCommunicationDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);

        /// <summary>
        /// 智能体_学生端口语交际对话
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task AgentStudentOralCommunicationSubmit(AgentStudentOralCommunicationSubmitInput input);
    }
}
