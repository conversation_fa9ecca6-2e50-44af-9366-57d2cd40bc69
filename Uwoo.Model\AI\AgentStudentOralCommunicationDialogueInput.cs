﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体_学生端口语交际对话入参
    /// </summary>
    public class AgentStudentOralCommunicationDialogueInput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? AgentTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 对话消息
        /// </summary>
        public string? Msg { get; set; }

        /// <summary>
        /// 语音文件
        /// </summary>
        public string? AudioUrl { get; set; }

        /// <summary>
        /// 语音时长（单位:秒）
        /// </summary>
        public int Duration { get; set; }
    }
}
