﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_学生端首页
    /// </summary>
    public class AgentStudentHomePageService : ServiceBase<AI_AgentBaseInfo, IAgentStudentHomePageRepository>, IAgentStudentHomePageService, IDependency
    {
        #region DI

        private IBase_SemesterTimeService _semesterTimeService;
        public AgentStudentHomePageService(IBase_SemesterTimeService semesterTimeService)
        {
            _semesterTimeService = semesterTimeService;
        }

        #endregion

        /// <summary>
        /// 智能体_学生端首页智能体任务列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<PageReturn<AgentStudentHomePageAgentTaskOuput>> GetStudentHomePageAgentTaskList(AgentStudentHomePageAgentTaskInput input)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                DateTime beginTime = DateTime.Now;
                DateTime endTime = DateTime.Now;
                if (nowSemesterTime.NowTerm == 1)
                {
                    beginTime = nowSemesterTime.FallStartDate.Value;
                    endTime = nowSemesterTime.FallEndDate.Value;
                }
                else
                {
                    beginTime = nowSemesterTime.SpringStartDate.Value;
                    endTime = nowSemesterTime.SpringEndDate.Value;
                }

                //参数
                List<SugarParameter> parameters = new List<SugarParameter>();
                parameters.Add(new SugarParameter("@studentId", input.StudentId));
                parameters.Add(new SugarParameter("@beginTime", beginTime));
                parameters.Add(new SugarParameter("@endTime", endTime));
                parameters.Add(new SugarParameter("@classId", input.ClassId));

                string where = string.Empty;
                if (input.AgentTaskState == 1)
                {
                    where += " AND pub.EndTime> GETDATE( ) ";
                }
                else
                {
                    where += " AND pub.EndTime<= GETDATE( ) ";
                }

                if (!string.IsNullOrEmpty(input.SubjectId))
                {
                    where += " AND task.SubjectId= @subjectId ";
                    parameters.Add(new SugarParameter("@subjectId", input.SubjectId));
                }

                //获取数据
                string sql = $@"SELECT
                                	agent.Id AS AgentId,
                                	COALESCE ( task.TaskLogo, agent.Logo ) AS AgentLogo,
                                	agent.AgentBotCode,
                                	task.Id AS AgentTaskId,
                                	task.Name AS AgentTaskName,
                                	task.Introduce AS AgentTaskIntroduce,
                                    CASE WHEN pub.EndTime <= GETDATE( ) THEN 2 ELSE 1 END AS AgentTaskState,
                                    CASE WHEN task.AgentTaskType = 1 THEN CASE WHEN stuDo.Id IS NULL THEN 2 ELSE 1 END 
                                		 WHEN task.AgentTaskType = 2 THEN CASE WHEN s.StageCount!= 0 AND s.StageCount = d.DoCount THEN 1 ELSE 2 END
                                		 ELSE 2 END AS StudentDoTaskState,
                                	pub.CreateTime,
                                	pub.BeginTime,
                                	pub.EndTime,
                                	agent.AgentName 
                                FROM
                                	AI_AgentTaskPublish pub WITH ( NOLOCK )
                                	INNER JOIN Exam_Class class WITH ( NOLOCK ) ON pub.PublishBusinessId = class.Id
                                	INNER JOIN AI_AgentTask task WITH ( NOLOCK ) ON pub.AgentTaskId = task.Id 
                                	AND task.IsDeleted = 0
                                	INNER JOIN AI_AgentBaseInfo agent WITH ( NOLOCK ) ON agent.Id = task.AgentId 
                                	AND agent.IsDeleted = 0
                                	LEFT JOIN AI_StudentDoAgentTaskInfo stuDo WITH ( NOLOCK ) ON stuDo.AgentTaskId = task.Id 
                                	AND stuDo.StudentId = @studentId
                                	AND stuDo.IsDeleted = 0 
                                	OUTER APPLY ( SELECT COUNT ( 1 ) AS StageCount FROM AI_ProjectStageTask WHERE ProjectId = task.Id AND IsDeleted = 0 ) s 
                                    OUTER APPLY ( SELECT COUNT ( DISTINCT ProjectStageTaskId ) AS DoCount FROM AI_StudentDoProjectTask WHERE ProjectId = task.Id AND IsDeleted = 0 AND StudentId =@studentId AND IsStandard = 1 ) d 
                                WHERE
                                	pub.IsDeleted = 0 
                                	AND pub.BeginTime >= @beginTime 
                                	AND pub.EndTime <= @endTime 
                                	AND pub.PublishBusinessId = @classId
                                	{where}";
                RefAsync<int> totalNumber = 0;
                List<AgentStudentHomePageAgentTaskOuput> agentStudentHomePages = await DBSqlSugar.SqlQueryable<AgentStudentHomePageAgentTaskOuput>(sql)
                    .AddParameters(parameters)
                    .OrderBy("CreateTime DESC")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                return new PageReturn<AgentStudentHomePageAgentTaskOuput>()
                {
                    Datas = agentStudentHomePages,
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
