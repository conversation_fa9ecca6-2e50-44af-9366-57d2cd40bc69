<?xml version="1.0"?>
<doc>
    <assembly>
        <name>UwooAgent.WebApi</name>
    </assembly>
    <members>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentCommonController">
            <summary>
            智能体_通用功能
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.AIUploadFile">
            <summary>
            AI上传文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.AIGenerateImage(UwooAgent.Model.AI.AIGenerateImageInput)">
            <summary>
            AI_生成图片
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.AIGenerateHTMLCode(UwooAgent.Model.AI.AIGenerateHTMLCodeInput)">
            <summary>
            AI_生成HTML代码
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.GetAIFileInfoList">
            <summary>
            获取AI文件消息
            </summary>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.AudioFileChangeText(UwooAgent.Model.AI.AudioFileChangeTextInput)">
            <summary>
            音频文件转文本
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.TextChangeVoice(UwooAgent.Model.AI.AITextChangeVoiceInput,System.Threading.CancellationToken)">
            <summary>
            文本转语音
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.GetAIDialogueContentRecord(UwooAgent.Model.AI.GetAIDialogueContentRecordInput)">
            <summary>
            获取AI对话内容记录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentCommonController.GetAgentModelInfo">
            <summary>
            获取智能体模型信息
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController">
            <summary>
            智能体_智能出题
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GetQuestionTypes">
            <summary>
            获取题型列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GetDifficultyLevels">
            <summary>
            获取难度等级列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GetQuestionDirections">
            <summary>
            获取出题方向列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GetKnowledgePointsBySubject(System.String)">
            <summary>
            根据学科获取知识点列表
            </summary>
            <param name="subjectId">学科ID</param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GenerateQuestions(UwooAgent.Model.AI.IntelligentQuestionGenerationInput)">
            <summary>
            智能出题
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.GenerateQuestionsStream(UwooAgent.Model.AI.IntelligentQuestionGenerationInput,System.Threading.CancellationToken)">
            <summary>
            智能出题（SSE流式返回）
            </summary>
            <param name="input"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.RegenerateQuestion(UwooAgent.Model.AI.RegenerateQuestionInput)">
            <summary>
            重新生成单题
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.SaveSingleQuestionToBank(UwooAgent.Model.AI.SaveSingleQuestionToBankInput)">
            <summary>
            单题保存到题库
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentIntelligentQuestionController.SaveBatchQuestionsToBank(UwooAgent.Model.AI.SaveBatchQuestionsToBankInput)">
            <summary>
            批量保存到题库
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentStudentHomePageController">
            <summary>
            智能体_学生端首页控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentHomePageController.GetStudentHomePageAgentTaskList(UwooAgent.Model.AI.AgentStudentHomePageAgentTaskInput)">
            <summary>
            智能体_学生端首页智能体任务列表
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentStudentOralCommunicationController">
            <summary>
            智能体_学生端语交际
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentOralCommunicationController.AgentStudentOralCommunicationDialogue(UwooAgent.Model.AI.AgentStudentOralCommunicationDialogueInput,System.Threading.CancellationToken)">
            <summary>
            智能体_学生端口语交际对话
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentStudentOralCommunicationController.AgentStudentOralCommunicationSubmit(UwooAgent.Model.AI.AgentStudentOralCommunicationSubmitInput)">
            <summary>
            智能体_学生端口语交际提交
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherHomePageController">
            <summary>
            智能体_教师首页控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherHomePageController.GetAgentListInfo(UwooAgent.Model.AI.AgentTeacherHomePageListInput)">
            <summary>
            获取智能体列表信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherHomePageController.AgentCollection(UwooAgent.Model.AI.AgentTeacherCollectionInput)">
            <summary>
            智能体收藏
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController">
            <summary>
            智能体_教师口语交际
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.SaveTeacherOralCommunication(UwooAgent.Model.AI.SaveTeacherOralCommunicationInput)">
            <summary>
            教师保存/编辑口语交际
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.GetOralCommunicationDetail(UwooAgent.Model.AI.AgentOralCommunicationDetailInput)">
            <summary>
            获取智能体口语交际详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.DelOralCommunication(UwooAgent.Model.AI.DelOralCommunicationInput)">
            <summary>
            删除口语交际任务
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.GetOralCommunicationList(UwooAgent.Model.AI.AgentTeacherOralCommunicationListInput)">
            <summary>
            智能体_教师端口语交际列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.GetOralCommunicationAnalyse(UwooAgent.Model.AI.GetOralCommunicationAnalyseInput)">
            <summary>
            智能体_教师端获取智能体任务分析
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherOralCommunicationController.GetOralCommunicationStudentResult(UwooAgent.Model.AI.GetOralCommunicationStudentResultInput)">
            <summary>
            教师获取学生口语交际评估结果
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController">
            <summary>
            智能体_教师端项目化实践
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.SaveProjectTaskInfo(UwooAgent.Model.AI.SaveProjectTaskInfoInput)">
            <summary>
            保存/编辑项目化实践任务
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.GetProjectTaskDetails(UwooAgent.Model.AI.ProjectTaskDetailsInput)">
            <summary>
            获取项目化实践详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.DelProjectTask(UwooAgent.Model.AI.DelProjectTaskInput)">
            <summary>
            删除项目化实践任务
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.PublishProjectTaskToClass(UwooAgent.Model.AI.PublishProjectTaskToClassInput)">
            <summary>
            发布项目化实践任务到班级
            </summary>
            <param name="input">发布参数</param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.UnpublishProjectTask(UwooAgent.Model.AI.UnpublishProjectTaskInput)">
            <summary>
            撤销项目化实践任务发布
            </summary>
            <param name="input">撤销发布参数</param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.GetProjectTaskList(UwooAgent.Model.AI.GetProjectTaskListInput)">
            <summary>
            获取项目化实践列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherProjectController.AICreateTaskProperty(UwooAgent.Model.AI.AICreateTaskPropertyInput,System.Threading.CancellationToken)">
            <summary>
            AI创建项目化实践阶段任务相关属性
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController">
            <summary>
            智能体_教师端教案
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.SaveTeachingPlanContentDemand(UwooAgent.Model.AI.SaveTeachingPlanContentDemandInput)">
            <summary>
            保存教案内容要求
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.DelTeachingPlanContentDemand(UwooAgent.Model.AI.DelTeachingPlanContentDemandInput)">
            <summary>
            删除教案内容要求
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.GetTeachingPlanContentDemand(UwooAgent.Model.AI.GetTeachingPlanContentDemandInput)">
            <summary>
            获取教案内容要求
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.CreateTeachingPlan(UwooAgent.Model.AI.CreateTeachingPlanInput,System.Threading.CancellationToken)">
            <summary>
            创建教案
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.UpdateTeachingPlanCreateRecord(UwooAgent.Model.AI.UpdateTeachingPlanCreateRecordInput)">
            <summary>
            修改教案创建记录中的教案文本
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.GetTeachingPlanCreateRecord(UwooAgent.Model.AI.GetTeachingPlanCreateRecordInput)">
            <summary>
            获取教案创建记录列表
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:Uwoo.Model.CustomException.BusException"></exception>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordOptimize(UwooAgent.Model.AI.TeachingPlanCreateRecordOptimizeInput,System.Threading.CancellationToken)">
            <summary>
            教案创建记录文本润色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordDetails(UwooAgent.Model.AI.TeachingPlanCreateRecordDetailsInput)">
            <summary>
            教案创建记录详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordTextToWord(UwooAgent.Model.AI.TeachingPlanCreateRecordTextToWordInput)">
            <summary>
            教案生成记录文本转word
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordFileUpdateName(UwooAgent.Model.AI.TeachingPlanCreateRecordFileUpdateNameInput)">
            <summary>
            教案生成记录修改文件名称
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanCreateRecordFile(UwooAgent.Model.AI.TeachingPlanCreateRecordFileInput)">
            <summary>
            获取教案生成记录文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.SaveTeachingPlan(UwooAgent.Model.AI.SaveTeachingPlanInput)">
            <summary>
            保存教案
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.GetTeachingPlan(UwooAgent.Model.AI.GetTeachingPlanInput)">
            <summary>
            获取教案列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.GetTeachingPlanDetails(UwooAgent.Model.AI.GetTeachingPlanDetailsInput)">
            <summary>
            获取教案详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanTextUpdate(UwooAgent.Model.AI.TeachingPlanTextUpdateInput)">
            <summary>
            教案文本类型编辑
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.TeachingPlanUpdateName(UwooAgent.Model.AI.TeachingPlanUpdateNameInput)">
            <summary>
            教案重命名
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AgentTeacherTeachingPlanController.DelTeachingPlan(UwooAgent.Model.AI.DelTeachingPlanInput)">
            <summary>
            删除教案
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.AIDirectiveController">
            <summary>
            AI指令管理控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.GetList(UwooAgent.Model.AI.AIDirectiveQueryInput)">
            <summary>
            获取AI指令分页列表
            </summary>
            <param name="input">查询参数</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.GetDetail(System.String)">
            <summary>
            根据ID获取AI指令详情
            </summary>
            <param name="id">指令ID</param>
            <returns>指令详情</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Create(UwooAgent.Model.AI.CreateAIDirectiveInput)">
            <summary>
            创建AI指令
            </summary>
            <param name="input">创建参数</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Update(UwooAgent.Model.AI.UpdateAIDirectiveInput)">
            <summary>
            更新AI指令
            </summary>
            <param name="input">更新参数</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Delete(System.String)">
            <summary>
            删除AI指令（软删除）
            </summary>
            <param name="id">指令ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Restore(System.String)">
            <summary>
            恢复已删除的AI指令
            </summary>
            <param name="id">指令ID</param>
            <returns>恢复结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.GetTypeStatistics">
            <summary>
            获取AI指令类型统计
            </summary>
            <returns>类型统计</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.Search(System.String,System.Int32)">
            <summary>
            搜索AI指令
            </summary>
            <param name="keyword">搜索关键词</param>
            <param name="limit">返回数量限制</param>
            <returns>搜索结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.AIDirectiveController.BatchDelete(System.String[])">
            <summary>
            批量删除AI指令
            </summary>
            <param name="ids">指令ID列表</param>
            <returns>批量删除结果</returns>
        </member>
        <member name="T:UwooAgent.WebApi.Controllers.AI.KnowledgeController">
            <summary>
            知识库管理控制器
            </summary>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.GetList(UwooAgent.Model.AI.KnowledgeQueryInput)">
            <summary>
            获取知识库分页列表
            </summary>
            <param name="input">查询参数</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.GetDetail(System.String)">
            <summary>
            根据ID获取知识库详情
            </summary>
            <param name="id">知识库ID</param>
            <returns>知识库详情</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.Create(UwooAgent.Model.AI.CreateKnowledgeInput)">
            <summary>
            创建知识库
            </summary>
            <param name="input">创建参数</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.Update(UwooAgent.Model.AI.UpdateKnowledgeInput)">
            <summary>
            更新知识库
            </summary>
            <param name="input">更新参数</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.Delete(System.String)">
            <summary>
            删除知识库
            </summary>
            <param name="id">知识库ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.CheckName(System.String,System.String)">
            <summary>
            检查知识库名称是否存在
            </summary>
            <param name="name">知识库名称</param>
            <param name="excludeId">排除的ID（用于更新时检查）</param>
            <returns>检查结果</returns>
        </member>
        <member name="M:UwooAgent.WebApi.Controllers.AI.KnowledgeController.GetStatistics">
            <summary>
            获取知识库统计信息
            </summary>
            <returns>统计信息</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub" -->
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.#ctor(Uwoo.Core.CacheManager.IService.ICacheService)">
            <summary>
            构造 注入
            </summary>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.OnConnectedAsync">
            <summary>
            建立连接时异步触发
            </summary>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            离开连接时异步触发
            </summary>
            <param name="ex"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.GetCnnectionIds(System.String)">
            <summary>
            根据用户名获取所有的客户端
            </summary>
            <param name="username"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.SendHomeMessage(System.String,System.String,System.String)">
            <summary>
            发送给指定的人
            </summary>
            <param name="username">sys_user表的登陆帐号</param>
            <param name="message">发送的消息</param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Hubs.HomePageMessageHub.UserOffline">
            <summary>
            断开连接
            </summary>
            <returns></returns>
        </member>
        <member name="T:Uwoo.WebApi.Controllers.Login.LoginController">
            <summary>
            登录控制器
            </summary>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.Login.LoginController.SubmitLogin(Uwoo.Model.Login.SubmitLoginInput)">
            <summary>
            登录
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController">
             <summary>
            1、普通参数校验只需要标识属性：[ObjectGeneralValidatorFilter(ValidatorGeneral.xxx,ValidatorGeneral.xxx)]，
            需要在ValidatorGeneral枚举中添加枚举值(参数名)，并在UseMethodsGeneralParameters方法中注入进去即可在任何地方重复使用
             
             2、model校验只需要标识属性[ObjectModelValidatorFilter(ValidatorModel.xxx)]
             需要在ValidatorModel枚举中添加枚举值(参数名)，
             并在UseMethodsModelParameters方法中注入进去(注入时可以指定需要验证的字段)即可在任何地方重复使用
             --如果其他方法使用的是同一个model，但验证的字段不同，在ValidatorModel重新添加一个枚举值，
             --并在UseMethodsModelParameters方法注入,添加新的指定字段即可
             </summary>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test1(System.String,System.String)">
            <summary>
            验证UserName与PhoneNo为必填
            </summary>
            <param name="userName"></param>
            <param name="phoneNo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test2(System.String,System.String)">
            <summary>
            验证PhoneNo为必填
            </summary>
            <param name="userName"></param>
            <param name="phoneNo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test3(System.String,System.String)">
            <summary>
            验证字符长度与值大小
            </summary>
            <param name="userName"></param>
            <param name="phoneNo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test4(Uwoo.Entity.DomainModels.System.LoginInfo)">
            <summary>
            Login配置的规则用户名与密码必填
            </summary>
            <param name="loginInfo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test5(Uwoo.Entity.DomainModels.System.LoginInfo)">
            <summary>
            LoginOnlyPassWord配置的规则密码必填
            </summary>
            <param name="loginInfo"></param>
            <returns></returns>
        </member>
        <member name="M:Uwoo.WebApi.Controllers.ObjectActionValidatorExampleController.Test6(Uwoo.Entity.DomainModels.System.LoginInfo,System.String)">
            <summary>
            同时验证实体LoginInfo与单个参数phoneNo
             Login配置的规则用户名与密码必填,手机号必填
            </summary>
            <param name="loginInfo"></param>
            <returns></returns>
        </member>
        <member name="T:Uwoo.WebApi.Controllers.OSS.AliOSSController">
            <summary>
            neuget包aliyun-net-sdk-core
            </summary>
        </member>
        <member name="T:Uwoo.WebApi.SwaggerDocTag">
            <summary>
            Swagger 注释帮助类
            </summary>
        </member>
        <member name="M:Uwoo.WebApi.SwaggerDocTag.Apply(Microsoft.OpenApi.Models.OpenApiDocument,Swashbuckle.AspNetCore.SwaggerGen.DocumentFilterContext)">
            <summary>
            添加附加注释
            </summary>
            <param name="swaggerDoc"></param>
            <param name="context"></param>
        </member>
    </members>
</doc>
