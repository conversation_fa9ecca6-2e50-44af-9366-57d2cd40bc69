﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_学生端项目化实践
    /// </summary>
    public interface IAgentStudentProjectService : IService<AI_AgentTask>
    {
        /// <summary>
        /// 获取学生端项目化实践信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetStudentProjectInfoOutput> GetStudentProjectInfo(GetStudentProjectInfoInput input);
    }
}
