﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体学生端首页输出
    /// </summary>
    public class AgentStudentHomePageAgentTaskOuput
    {
        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 智能体名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 智能体Logo
        /// </summary>
        public string? AgentLogo { get; set; }

        /// <summary>
        /// 智能体应用编码
        /// </summary>
        public string? AgentBotCode { get; set; }

        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? AgentTaskId { get; set; }

        /// <summary>
        /// 智能体任务名称
        /// </summary>
        public string? AgentTaskName { get; set; }

        /// <summary>
        /// 智能体任务背景
        /// </summary>
        public string? AgentTaskIntroduce { get; set; }

        /// <summary>
        /// 智能体任务状态（1进行中、2已结束）
        /// </summary>
        public int AgentTaskState { get; set; }

        /// <summary>
        /// 学生做智能体任务状态（1已完成、2未完成）
        /// </summary>
        public int StudentDoTaskState { get; set; }

        /// <summary>
        /// 任务周期（开始时间）
        /// </summary>
        public string? BeginTime { get; set; }

        /// <summary>
        /// 任务周期（结束时间）
        /// </summary>
        public string? EndTime { get; set; }
    }
}
