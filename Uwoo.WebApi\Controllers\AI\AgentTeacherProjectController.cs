﻿using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;
using UwooAgent.System.Services.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_教师端项目化实践
    /// </summary>
    [Route("/AgentTeacherProject/[controller]/[action]")]
    [ApiController]
    public class AgentTeacherProjectController : ApiBaseController<IAgentTeacherProjectService>
    {
        #region DI
        private readonly IAgentTeacherProjectService _agentTeacherProjectService;
        public AgentTeacherProjectController(IAgentTeacherProjectService agentTeacherProjectService)
        {
            _agentTeacherProjectService = agentTeacherProjectService;
        }
        #endregion

        /// <summary>
        /// 保存/编辑项目化实践任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task SaveProjectTaskInfo(SaveProjectTaskInfoInput input)
        {
            if (input == null)
            {
                throw new BusException("入参不能为空！");
            }

            // 基础信息校验
            if (string.IsNullOrWhiteSpace(input.AgentId))
            {
                throw new BusException("智能体Id不能为空！");
            }
            // 项目名称：50字内
            if (string.IsNullOrWhiteSpace(input.Name))
            {
                throw new BusException("项目名称不能为空！");
            }
            if (input.Name.Length > 50)
            {
                throw new BusException("项目名称长度不能超过50个字符！");
            }
            // 项目背景介绍：500字内
            if (!string.IsNullOrWhiteSpace(input.Introduce) && input.Introduce.Length > 500)
            {
                throw new BusException("项目背景介绍长度不能超过500个字符！");
            }
            if (string.IsNullOrWhiteSpace(input.SubjectId))
            {
                throw new BusException("学科Id不能为空！");
            }
            if (string.IsNullOrWhiteSpace(input.GradeId))
            {
                throw new BusException("年级Id不能为空！");
            }
            if (string.IsNullOrWhiteSpace(input.TeacherId))
            {
                throw new BusException("教师Id不能为空！");
            }
            if (string.IsNullOrWhiteSpace(input.SchoolId))
            {
                throw new BusException("学校Id不能为空！");
            }

            // 发布信息校验
            if (input.IsPublish)
            {
                if (input.ClassId == null || input.ClassId.Count == 0)
                {
                    throw new BusException("发布状态下必须选择至少一个班级！");
                }
                if (input.TimeRange == null || input.TimeRange.Count != 2)
                {
                    throw new BusException("发布状态下必须设置有效的时间范围（开始时间和结束时间）！");
                }
                if (input.TimeRange[0] >= input.TimeRange[1])
                {
                    throw new BusException("发布开始时间必须早于结束时间！");
                }
            }

            // 阶段信息校验（至少一个阶段）
            if (input.ProjectStageInfos == null || input.ProjectStageInfos.Count == 0)
            {
                throw new BusException("至少需要添加一个项目化实践阶段！");
            }

            foreach (var stage in input.ProjectStageInfos)
            {
                if (stage == null)
                {
                    throw new BusException("项目化实践阶段信息不能为null！");
                }

                // 阶段名称：50字内
                if (string.IsNullOrWhiteSpace(stage.Name))
                {
                    throw new BusException("阶段名称不能为空！");
                }
                if (stage.Name.Length > 50)
                {
                    throw new BusException("阶段名称长度不能超过50个字符！");
                }
                // 阶段目标（描述）：100字内
                if (!string.IsNullOrWhiteSpace(stage.Describe) && stage.Describe.Length > 100)
                {
                    throw new BusException("阶段描述（目标）长度不能超过100个字符！");
                }

                // 阶段任务校验（至少一个任务）
                if (stage.ProjectStageTaskInfos == null || stage.ProjectStageTaskInfos.Count == 0)
                {
                    throw new BusException($"阶段「{stage.Name}」至少需要添加一个任务！");
                }

                foreach (var task in stage.ProjectStageTaskInfos)
                {
                    if (task == null)
                    {
                        throw new BusException("项目化实践阶段任务信息不能为null！");
                    }

                    // 任务名称：30字
                    if (string.IsNullOrWhiteSpace(task.Name))
                    {
                        throw new BusException($"阶段「{stage.Name}」的任务名称不能为空！");
                    }
                    if (task.Name.Length > 30)
                    {
                        throw new BusException($"阶段「{stage.Name}」的任务名称长度不能超过30个字符！");
                    }
                    // 任务目标：200字
                    if (string.IsNullOrWhiteSpace(task.Target))
                    {
                        throw new BusException($"任务「{task.Name}」的目标不能为空！");
                    }
                    if (task.Target.Length > 200)
                    {
                        throw new BusException($"任务「{task.Name}」的目标长度不能超过200个字符！");
                    }

                    // 任务类型校验
                    if (!new[] { 1, 2, 3 }.Contains(task.TaskType))
                    {
                        throw new BusException($"任务「{task.Name}」的任务类型只能是1（成果评估）、2（情景对话）、3（知识问答）！");
                    }
                    if (task.TaskType == 1)
                    {
                        // 评分标准：100字
                        if (string.IsNullOrWhiteSpace(task.ScoreStandard))
                        {
                            throw new BusException($"任务「{task.Name}」的评分标准不能为空！");
                        }
                        if (task.ScoreStandard.Length > 100)
                        {
                            throw new BusException($"任务「{task.Name}」的评分标准长度不能超过100个字符！");
                        }
                    }

                    if (task.TaskType == 2)
                    {
                        // 评分标准：100字
                        if (string.IsNullOrWhiteSpace(task.ScoreStandard))
                        {
                            throw new BusException($"任务「{task.Name}」的评分标准不能为空！");
                        }
                        if (task.ScoreStandard.Length > 100)
                        {
                            throw new BusException($"任务「{task.Name}」的评分标准长度不能超过100个字符！");
                        }

                        // 情景对话要求：1000字
                        if (string.IsNullOrWhiteSpace(task.Demand))
                        {
                            throw new BusException($"任务「{task.Name}」的要求（情景对话要求）不能为空！");
                        }
                        if (task.Demand.Length > 1000)
                        {
                            throw new BusException($"任务「{task.Name}」的要求（情景对话要求）长度不能超过1000个字符！");
                        }
                    }

                    if (task.TaskType == 3)
                    {
                        // 问答范围：2000字
                        if (string.IsNullOrWhiteSpace(task.Scope))
                        {
                            throw new BusException($"任务「{task.Name}」的问答范围不能为空！");
                        }
                        if (task.Scope.Length > 2000)
                        {
                            throw new BusException($"任务「{task.Name}」的问答范围长度不能超过2000个字符！");
                        }
                    }

                    // 评估角色设定：2000字
                    if (string.IsNullOrWhiteSpace(task.RoleSetting))
                    {
                        throw new BusException($"任务「{task.Name}」的角色设定不能为空！");
                    }
                    if (task.RoleSetting.Length > 2000)
                    {
                        throw new BusException($"任务「{task.Name}」的角色设定长度不能超过2000个字符！");
                    }

                    // AI评估条件默认分数（开启时默认60）
                    if (task.GroupIsAssessment && task.GroupAssessmentScore <= 0)
                    {
                        task.GroupAssessmentScore = 60;
                    }
                    if (task.TaskIsAssessment && task.TaskAssessmentScore <= 0)
                    {
                        task.TaskAssessmentScore = 60;
                    }

                    // 任务高频问题校验（至少一个问题）
                    if (task.QuestionInfos == null || task.QuestionInfos.Count == 0)
                    {
                        throw new BusException($"任务「{task.Name}」至少需要添加一个高频问题！");
                    }

                    foreach (var question in task.QuestionInfos)
                    {
                        if (question == null)
                        {
                            throw new BusException("任务高频问题信息不能为null！");
                        }

                        if (string.IsNullOrWhiteSpace(question.Name))
                        {
                            throw new BusException($"任务「{task.Name}」的高频问题名称不能为空！");
                        }

                        if (!string.IsNullOrWhiteSpace(question.Describe) && question.Describe.Length > 300)
                        {
                            throw new BusException($"任务「{task.Name}」的高频问题描述长度不能超过300个字符！");
                        }
                    }
                }
            }
            await _agentTeacherProjectService.SaveProjectTaskInfo(input);
        }

        /// <summary>
        /// 获取项目化实践详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ProjectTaskDetailsOutput> GetProjectTaskDetails(ProjectTaskDetailsInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("项目化实践任务Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            return await _agentTeacherProjectService.GetProjectTaskDetails(input);
        }

        /// <summary>
        /// 删除项目化实践任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        [HttpPost]
        public async Task DelProjectTask(DelProjectTaskInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("项目化实践任务Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            await _agentTeacherProjectService.DelProjectTask(input);
        }

        /// <summary>
        /// 发布项目化实践任务到班级
        /// </summary>
        /// <param name="input">发布参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task PublishProjectTaskToClass(PublishProjectTaskToClassInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("项目化实践任务Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }

            if (input.ClassIds == null || !input.ClassIds.Any())
            {
                throw new BusException("至少需要选择一个班级!");
            }

            if (input.TimeRange == null || input.TimeRange.Count != 2 || !input.TimeRange[0].HasValue || !input.TimeRange[1].HasValue)
            {
                throw new BusException("请提供有效的时间范围!");
            }

            if (input.TimeRange[0].Value >= input.TimeRange[1].Value)
            {
                throw new BusException("开始时间必须小于结束时间!");
            }
            await _agentTeacherProjectService.PublishProjectTaskToClasses(input);
        }

        /// <summary>
        /// 撤销项目化实践任务发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task UnpublishProjectTask(UnpublishProjectTaskInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("项目化实践任务Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.TeacherId))
            {
                throw new BusException("教师Id不能为空!");
            }
            await _agentTeacherProjectService.UnpublishProjectTask(input);
        }

        /// <summary>
        /// 获取项目化实践列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<PageReturn<GetProjectTaskListOutput>> GetProjectTaskList(GetProjectTaskListInput input)
        {
            if (string.IsNullOrEmpty(input.TeacherId) || string.IsNullOrEmpty(input.SchoolId) || string.IsNullOrEmpty(input.AgentId) || string.IsNullOrEmpty(input.SubjectId) || string.IsNullOrEmpty(input.GradeId))
            {
                throw new BusException("参数异常!", 801);
            }
            if (input.PageIndex <= 0)
            {
                input.PageIndex = 1;
            }
            if (input.PageSize <= 0)
            {
                input.PageSize = 10;
            }
            if (input.PublishStatus != 1 && input.PublishStatus != 2)
            {
                input.PublishStatus = 0;
            }
            return await _agentTeacherProjectService.GetProjectTaskList(input);
        }

        /// <summary>
        /// AI创建项目化实践阶段任务相关属性
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task AICreateTaskProperty(AICreateTaskPropertyInput input, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Name)
                    || string.IsNullOrEmpty(input.Target)
                    || string.IsNullOrEmpty(input.SubjectId)
                    || string.IsNullOrEmpty(input.GradeId))
                {
                    throw new BusException("参数异常!", 801);
                }
                if (input.TaskType != 1 && input.TaskType != 2 && input.TaskType != 3)
                {
                    throw new BusException("参数异常!", 801);
                }

                //设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");

                await _agentTeacherProjectService.AICreateTaskProperty(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }
                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                //SSE推送客户端
                string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                await Response.WriteAsync(ssePushData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }
    }
}
