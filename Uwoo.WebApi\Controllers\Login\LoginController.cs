﻿using Coldairarrow.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Uwoo.Core.Attributes;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.Login;
using Uwoo.System.IServices;
using Uwoo.System.IServices.Login;

namespace Uwoo.WebApi.Controllers.Login
{
    /// <summary>
    /// 登录控制器
    /// </summary>
    [Route("/Login/Login/[action]")]
    public class LoginController : ApiBaseController<ILoginService>
    {
        #region DI
        private readonly ILoginService _loginService;
        public LoginController(ILoginService loginService)
        {
            _loginService = loginService;
        }
        #endregion

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [NoCheckJWT]
        public SubmitLoginOutput SubmitLogin([FromBody] SubmitLoginInput input)
        {
            if (string.IsNullOrEmpty(input.PhoneNo))
            {
                return new SubmitLoginOutput()
                {
                    IsLogin = false,
                    Msg = "请输入手机号/用户名!"
                };
            }
            if (string.IsNullOrEmpty(input.Password))
            {
                return new SubmitLoginOutput()
                {
                    IsLogin = false,
                    Msg = "请输入密码!"
                };
            }
            if(!input.RequestAgent.HasValue)
            {
                input.RequestAgent = RequestAgent;
            }
            return _loginService.SubmitLogin(input);
        }
    }
}
