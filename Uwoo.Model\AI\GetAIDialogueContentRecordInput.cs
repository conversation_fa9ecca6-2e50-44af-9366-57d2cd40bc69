﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 获取或删除AI对话内容记录入参
    /// </summary>
    public class GetAIDialogueContentRecordInput
    {
        /// <summary>
        /// 分页_页码默认1
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 分页_每页数量默认3
        /// </summary>
        public int PageSize { get; set; } = 3;

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 智能体任务Id
        /// </summary>
        public string? AgentTaskId { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 0学生端口语交际
        /// </summary>
        public int Type { get; set; }
    }
}
