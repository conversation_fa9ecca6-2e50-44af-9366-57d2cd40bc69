// Uwoo.WebApi/Startup.cs
using Autofac;
using Mapster;
using MapsterMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Quartz;
using Quartz.Impl;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using Coldairarrow.Util;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.Configuration;
using Uwoo.Core.DBManager;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.Filters;
using Uwoo.Core.Middleware;
using Uwoo.Core.ObjectActionValidator;
using Uwoo.Core.Quartz;
using Uwoo.WebApi.Controllers.Hubs;
using System.Security.Cryptography;
using Uwoo.Core.RabbitMQ;
using Uwoo.Core.LogManager;
using Microsoft.AspNetCore.Hosting.Server.Features;
using Uwoo.Contracts.Config;
using CSRedis;
using Uwoo.Mongo.Services.Redis;
using Microsoft.Extensions.Logging;
using NLog.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Uwoo.WebApi
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }
        private IServiceCollection Services { get; set; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            // 初始化模型验证配置
            services.UseMethodsModelParameters().UseMethodsGeneralParameters();
            services.AddSingleton<IObjectModelValidator>(new NullObjectModelValidator());
            Services = services;

            services.AddSession();
            services.AddMemoryCache();
            services.AddHttpContextAccessor();

            services.AddMvc(options =>
            {
                options.Filters.Add(typeof(ApiAuthorizeFilter));
                options.Filters.Add(typeof(ActionExecuteFilter));
                options.Filters.Add(typeof(GlobalExceptionFilter));
            });

            services.AddControllers()
              .AddNewtonsoftJson(op =>
              {
                  // 移除 CamelCasePropertyNamesContractResolver，使用默认的 PascalCase 命名（大写字母开头）
                  // op.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver();
                  op.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
              });

            // 配置 JWT 认证
            ConfigureJwtAuthentication(services);

            // 配置 CORS
            ConfigureCors(services);

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddControllers().AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = null;
            });

            // 配置 Swagger
            ConfigureSwagger(services);

            services.AddSignalR();
            InitId();

            services.AddHttpClient();
            Services.AddTransient<HttpResultfulJob>();
            Services.AddSingleton<ISchedulerFactory, StdSchedulerFactory>();
            Services.AddSingleton<Quartz.Spi.IJobFactory, IOCJobFactory>();
            services.AddScoped<IAccessLogger, NLogAccessLogger>();
            // 注册 RabbitMQ 连接工厂
            services.AddSingleton<RabbitMQConnectionFactory>();
            // 注册 RabbitMQ 消息生产者
            services.AddTransient<RabbitMQProducer>();
            //// 注册 RabbitMQ 消费者
            // services.AddSingleton<RabbitMQConsumer>();
            services.AddWebSocketManager(); // 添加 WebSocket 管理器

            //var config = new TypeAdapterConfig();
            //config.Scan(new[] { Assembly.GetAssembly(typeof(ModelMapperRegister)) });
            //services.AddSingleton(config);

            // 设置文件上传大小限制
            services.Configure<FormOptions>(x =>
            {
                x.MultipartBodyLengthLimit = 1024 * 1024 * 100;//100M
            });
            services.Configure<KestrelServerOptions>(options =>
            {
                options.Limits.MaxRequestBodySize = 1024 * 1024 * 100;//100M
            });
            services.Configure<IISServerOptions>(options =>
            {
                options.MaxRequestBodySize = 1024 * 1024 * 100;//100M
            });

            services.AddLogging(loggingBuilder =>
            {
                loggingBuilder.ClearProviders();
                loggingBuilder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information);
                loggingBuilder.AddNLog("nlog.config");
            });

            {
                var xredis = Configuration["Connection:RedisConnectionString"];
                //注册redis服务
                XRedisHelper.Initialization(new CSRedisClient(xredis + ",defaultDatabase=5"));
                RedisHelper.Initialization(new CSRedisClient(xredis + ",defaultDatabase=0"));
                services.AddSingleton<IMongoConfig>(x => x.GetRequiredService<IOptions<MongoConfig>>().Value);
                services.Configure<MongoConfig>(Configuration.GetSection(nameof(MongoConfig)));
                services.Configure<RabbitConfig>(Configuration.GetSection("RabbitMQ"));
            }
            AppSetting.Init(services, Configuration);
            services.AddHostedService<UwooJobBackgroundService>();
            services.UseSqlSugar();
        }

        // 配置 JWT 认证
        private void ConfigureJwtAuthentication(IServiceCollection services)
        {
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
             .AddJwtBearer(options =>
             {
                 options.TokenValidationParameters = new TokenValidationParameters
                 {
                     SaveSigninToken = true,//保存token,后台验证token是否生效(重要)
                     ValidateIssuer = true,//是否验证Issuer
                     ValidateAudience = true,//是否验证Audience
                     ValidateLifetime = true,//是否验证失效时间
                     ValidateIssuerSigningKey = true,//是否验证SecurityKey
                     ValidAudience = AppSetting.Secret.Audience,//Audience
                     ValidIssuer = AppSetting.Secret.Issuer,//Issuer，这两项和前面签发jwt的设置一致
                     IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(AppSetting.Secret.JWT))
                 };
                 options.Events = new JwtBearerEvents()
                 {
                     OnChallenge = context =>
                     {
                         context.HandleResponse();
                         context.Response.Clear();
                         context.Response.ContentType = "application/json";
                         context.Response.StatusCode = 401;
                         context.Response.WriteAsync(new { message = "授权未通过", status = false, code = 401 }.Serialize());
                         return Task.CompletedTask;
                     }
                 };
             });
        }

        // 配置 CORS
        private void ConfigureCors(IServiceCollection services)
        {
            string corsUrls = Configuration["CorsUrls"];
            if (string.IsNullOrEmpty(corsUrls))
            {
                throw new Exception("请配置跨请求的前端Url");
            }
            services.AddCors(options =>
            {
                options.AddDefaultPolicy(
                        builder =>
                        {
                            builder.AllowAnyOrigin()
                           .SetPreflightMaxAge(TimeSpan.FromSeconds(2520))
                            .AllowAnyHeader().AllowAnyMethod();
                        });
            });
        }



        // 配置 Swagger
        private void ConfigureSwagger(IServiceCollection services)
        {

            services.AddSwaggerGen(c =>
                {
                    var basePath = AppContext.BaseDirectory;
                    var xmlPath = Path.Combine(basePath, "UwooAgent.WebApi.xml");
                    c.SwaggerDoc("v1", new OpenApiInfo { Title = "UwooAgent.Core后台Api", Version = "v1", Description = "这是对文档的描述。。" });
                    c.SwaggerDoc("v2", new OpenApiInfo { Title = "UwooAgent.Core对外三方Api", Version = "v2", Description = "xxx接口文档" });
                    c.IncludeXmlComments(xmlPath, true);
                    //c.SwaggerDoc("v1", new OpenApiInfo { Title = "Api", Version = "v1", Description = "API", });
                    c.IncludeXmlComments(Path.Combine(basePath, "UwooAgent.Entity.xml"), true);//分层实体显示注释
                    c.IncludeXmlComments(Path.Combine(basePath, "UwooAgent.Model.xml"), true);//分层实体显示注释

                    // 配置 Swagger Schema ID
                    c.SchemaGeneratorOptions.SchemaIdSelector = type => type.FullName;

                    //   c.SchemaFilter<add>();
                    // 配置 servers
                    c.AddServer(new OpenApiServer
                    {
                        Url = "http://localhost:9910", // 您的 HTTPS 服务器地址
                        Description = "local server"
                    });
                    c.AddServer(new OpenApiServer
                    {
                        Url = "http://localhost:9910", // 生产服务器地址
                        Description = "test server"
                    });
                    var security = new Dictionary<string, IEnumerable<string>> { { AppSetting.Secret.Issuer, new string[] { } } };
                    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                    {
                        Description = "JWT授权token前面需要加上字段Bearer与一个空格,如Bearer token",
                        Name = "Authorization",
                        In = ParameterLocation.Header,
                        Type = SecuritySchemeType.ApiKey,
                        BearerFormat = "JWT",
                        Scheme = "Bearer"
                    });
                    c.AddSecurityRequirement(new OpenApiSecurityRequirement
                    {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        new string[] { }
                    }
                    });
                })
                 .AddControllers()
                .ConfigureApiBehaviorOptions(options =>
                {
                    options.SuppressConsumesConstraintForFormFileParameters = true;
                    options.SuppressInferBindingSourcesForParameters = false;
                    options.SuppressModelStateInvalidFilter = true;
                    options.SuppressMapClientErrors = true;
                    options.ClientErrorMapping[404].Link =
                        "https://*/404";
                });

        }


        public void ConfigureContainer(ContainerBuilder builder)
        {
            var singleton = typeof(Uwoo.Mongo.Interfaces.Lifecycle.ISingletonService);
            var impl_service = singleton.Assembly;
            builder.RegisterAssemblyTypes(impl_service).Where(x => singleton.IsAssignableFrom(x) && x != singleton)
                   .AsImplementedInterfaces()
                   .SingleInstance()
                   .PropertiesAutowired();

            Services.AddModule(builder, Configuration);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHostApplicationLifetime lifetime)
        {
            // 输出当前运行的端口
            lifetime.ApplicationStarted.Register(() =>
            {
                var serverAddressesFeature = app.ServerFeatures.Get<IServerAddressesFeature>();
                if (serverAddressesFeature != null)
                {
                    foreach (var address in serverAddressesFeature.Addresses)
                    {
                        Console.WriteLine($"Now listening on: {address}");
                    }
                }
            });
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                //app.UseQuartz(env);
            }
            app.UseMiddleware<LogMiddleware>();
            app.UseMiddleware<CorsMiddleware>();
            app.UseMiddleware<ExceptionHandlerMiddleWare>();
            app.UseDefaultFiles();
            app.UseStaticFiles().UseStaticFiles(new StaticFileOptions
            {
                ServeUnknownFileTypes = true
            });
            app.Use(HttpRequestMiddleware.Context);

            // 创建默认 upload 文件夹
            CreateUploadDirectory(env);

            app.UseStaticFiles(new StaticFileOptions()
            {
                FileProvider = new PhysicalFileProvider(
                Path.Combine(Directory.GetCurrentDirectory(), @"Upload")),
                RequestPath = "/Upload",
                OnPrepareResponse = (staticFile) =>
                {
                    //可以在此处读取请求的信息进行权限认证
                }
            });

            app.UseStaticHttpContext();
            var basePath = Path.GetDirectoryName(typeof(Program).Assembly.Location);//获取应用程序所在目录（绝对，不受工作目录影响，建议采用此方法获取路径）
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "UwooAgent.Core后台Api");
                c.SwaggerEndpoint("/swagger/v2/swagger.json", "测试第三方Api");
                c.RoutePrefix = "";
            });
            app.UseRouting();
            app.UseCors(option =>
            {
                option.AllowAnyHeader();
                option.AllowAnyOrigin();
                option.AllowAnyMethod();
            });
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseWebSockets(new WebSocketOptions() { KeepAliveInterval = TimeSpan.FromSeconds(120) });
            app.UseMiddleware<WebSocketServerMiddleware>(); // 使用自定义的 WebSocket 中间件
            // 启动 RabbitMQ 消费者 (测试)
            //var consumer = app.ApplicationServices.GetRequiredService<RabbitMQConsumer>();
            //lifetime.ApplicationStarted.Register(() =>
            //{
            //	Task.Run(() =>
            //	{
            //		consumer.ConsumeMessages("my_fanout_exchange", "my_queue", message =>
            //		{
            //			Console.WriteLine($"Received message: {message}");
            //			// 在这里处理你的消息
            //		});
            //	});
            //});

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                // 配置 SignalR
                if (AppSetting.UseSignalR)
                {
                    string corsUrls = Configuration["CorsUrls"];

                    endpoints.MapHub<HomePageMessageHub>("/message")
                    .RequireCors(t =>
                    t.WithOrigins(corsUrls.Split(',')).
                    AllowAnyMethod().
                    AllowAnyHeader().
                    AllowCredentials());
                }

            });
        }

        // 创建默认 upload 文件夹
        private void CreateUploadDirectory(IWebHostEnvironment env)
        {
            string _uploadPath = (env.ContentRootPath + "/Upload").ReplacePath();

            if (!Directory.Exists(_uploadPath))
            {
                Directory.CreateDirectory(_uploadPath);
            }
        }


        private static void InitId()
        {
            new IdHelperBootstrapper()
                .SetWorkderId(AppSetting.WorkId)
                .Boot();
        }
    }

    /// <summary>
    /// Swagger 注释帮助类
    /// </summary>
    public class SwaggerDocTag : IDocumentFilter
    {
        /// <summary>
        /// 添加附加注释
        /// </summary>
        /// <param name="swaggerDoc"></param>
        /// <param name="context"></param>
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            //添加对应的控制器描述
            swaggerDoc.Tags = new List<OpenApiTag>
            {
                new OpenApiTag { Name = "Test", Description = "这是描述" },
                //new OpenApiTag { Name = "你的控制器名字，不带Controller", Description = "控制器描述" },
            };
        }
    }
}