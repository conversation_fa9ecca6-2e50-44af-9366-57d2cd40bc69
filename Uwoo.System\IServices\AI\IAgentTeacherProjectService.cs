﻿using Coldairarrow.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Model.CustomException;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;

namespace UwooAgent.System.IServices.AI
{
    /// <summary>
    /// 智能体_教师端项目化实践
    /// </summary>
    public interface IAgentTeacherProjectService : IService<AI_AgentTask>
    {
        /// <summary>
        /// 保存/编辑项目化实践任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task SaveProjectTaskInfo(SaveProjectTaskInfoInput input);

        /// <summary>
        /// 获取项目化实践详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<ProjectTaskDetailsOutput> GetProjectTaskDetails(ProjectTaskDetailsInput input);

        /// <summary>
        /// 删除项目化实践任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        Task DelProjectTask(DelProjectTaskInput input);

        /// <summary>
        /// 发布项目化实践任务到班级
        /// </summary>
        /// <param name="input">发布参数</param>
        /// <returns></returns>
        Task PublishProjectTaskToClasses(PublishProjectTaskToClassInput input);

        /// <summary>
        /// 撤销项目化实践任务发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        Task UnpublishProjectTask(UnpublishProjectTaskInput input);

        /// <summary>
        /// 获取项目化实践列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageReturn<GetProjectTaskListOutput>> GetProjectTaskList(GetProjectTaskListInput input);

        /// <summary>
        /// AI创建项目化实践阶段任务相关属性
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task AICreateTaskProperty(AICreateTaskPropertyInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken);
    }
}
