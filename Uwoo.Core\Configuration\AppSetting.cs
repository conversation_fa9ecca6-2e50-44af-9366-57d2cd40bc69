﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System.IO;
using Uwoo.Core.Const;
using Uwoo.Core.Extensions;

namespace Uwoo.Core.Configuration
{
	public static class AppSetting
	{
		public static IConfiguration Configuration { get; private set; }

		public static string DbConnectionString
		{
			get { return _connection.DbConnectionString; }
		}

		public static string RedisConnectionString
		{
			get { return _connection.RedisConnectionString; }
		}

		public static bool UseRedis
		{
			get { return _connection.UseRedis; }
		}
		public static bool UseSignalR
		{
			get { return _connection.UseSignalR; }
		}
		public static Secret Secret { get; private set; }

		public static CreateMember CreateMember { get; private set; }

		public static ModifyMember ModifyMember { get; private set; }

		private static Connection _connection;

		public static string TokenHeaderName = "Authorization";

		/// <summary>
		/// Actions权限过滤
		/// </summary>
		public static GlobalFilter GlobalFilter { get; set; }

        /// <summary>
        /// kafka配置
        /// </summary>
        public static Kafka Kafka { get; set; }

		/// <summary>
		/// 华为云obs配置
		/// </summary>
		public static HuaweiObs HuaweiObs { get; set; }

		public static Upload Upload { get; set; }

		public static WPSConfig WPSConfig { get; set; }

		public static JPush JPushConfig { get; set; }

		public static PaperCenter PaperCenter { get; set; }

        public static DataCenter DataCenter { get; set; }

		public static CKDataCenter CKDataCenter { get; set; }

		/// <summary>
		/// JWT有效期(分钟=默认120)
		/// </summary>
		public static int ExpMinutes { get; private set; } = 120;

		public static long WorkId { get; private set; } = 100;
		public static string PuMa { get; private set; }

		public static string CurrentPath { get; private set; } = null;
		public static string DownLoadPath { get { return CurrentPath + "\\Download\\"; } }

		/// <summary>
		/// 是否显示sql日志
		/// </summary>
		public static bool ShowSqlLog { get; set; }

		public static DouBaoAI DouBaoAI { get; set; }

        public static OfficeTool OfficeTool { get; set; }

        /// <summary>
        /// 答题框
        /// </summary>
        public static string AnswerBox { get; set; }

		public static RabbitMQConfig RabbitMQConfig { get; set; }

		public static void Init(IServiceCollection services, IConfiguration configuration)
		{
			Configuration = configuration;
			services.Configure<Secret>(configuration.GetSection("Secret"));
			services.Configure<Connection>(configuration.GetSection("Connection"));
			services.Configure<CreateMember>(configuration.GetSection("CreateMember"));
			services.Configure<ModifyMember>(configuration.GetSection("ModifyMember"));
			services.Configure<GlobalFilter>(configuration.GetSection("GlobalFilter"));
            services.Configure<Kafka>(configuration.GetSection("Kafka"));
			services.Configure<HuaweiObs>(configuration.GetSection("HuaweiObs"));
			services.Configure<RabbitMQConfig>(configuration.GetSection("RabbitMQ"));
			services.Configure<Upload>(configuration.GetSection("Upload"));
			services.Configure<WPSConfig>(configuration.GetSection("WPSConfig"));
			services.Configure<JPush>(configuration.GetSection("JPushConfig"));
			services.Configure<PaperCenter>(configuration.GetSection("PaperCenter"));
            services.Configure<DataCenter>(configuration.GetSection("DataCenter"));
            services.Configure<CKDataCenter>(configuration.GetSection("CKDataCenter"));
            services.Configure<DouBaoAI>(configuration.GetSection("DouBaoAI"));
            services.Configure<OfficeTool>(configuration.GetSection("OfficeTool"));
            var provider = services.BuildServiceProvider();
			try
			{
				IWebHostEnvironment environment = provider.GetRequiredService<IWebHostEnvironment>();
				CurrentPath = Path.Combine(environment.ContentRootPath, "").ReplacePath();
			}
			catch (System.Exception)
			{

			}
			

			Secret = provider.GetRequiredService<IOptions<Secret>>().Value;
			
			//设置修改或删除时需要设置为默认用户信息的字段
			CreateMember = provider.GetRequiredService<IOptions<CreateMember>>().Value ?? new CreateMember();
			ModifyMember = provider.GetRequiredService<IOptions<ModifyMember>>().Value ?? new ModifyMember();
            GlobalFilter = provider.GetRequiredService<IOptions<GlobalFilter>>().Value ?? new GlobalFilter();
			WPSConfig = provider.GetRequiredService<IOptions<WPSConfig>>().Value ?? new WPSConfig();
			GlobalFilter.Actions = GlobalFilter.Actions ?? new string[0];
			Kafka = provider.GetRequiredService<IOptions<Kafka>>().Value ?? new Kafka();
			HuaweiObs = provider.GetRequiredService<IOptions<HuaweiObs>>().Value?? new HuaweiObs();
			Upload = provider.GetRequiredService<IOptions<Upload>>().Value ?? new Upload();
			RabbitMQConfig = provider.GetRequiredService<IOptions<RabbitMQConfig>>().Value ?? new RabbitMQConfig();
			_connection = provider.GetRequiredService<IOptions<Connection>>().Value;
            PaperCenter = provider.GetRequiredService<IOptions<PaperCenter>>().Value ?? new PaperCenter();
            DataCenter = provider.GetRequiredService<IOptions<DataCenter>>().Value ?? new DataCenter();
            CKDataCenter = provider.GetRequiredService<IOptions<CKDataCenter>>().Value ?? new CKDataCenter();
            JPushConfig = provider.GetRequiredService<IOptions<JPush>>().Value ?? new JPush();
			DouBaoAI = provider.GetRequiredService<IOptions<DouBaoAI>>().Value ?? new DouBaoAI();
            OfficeTool = provider.GetRequiredService<IOptions<OfficeTool>>().Value ?? new OfficeTool();
            ShowSqlLog = configuration["ShowSqlLog"] == "1";
			AnswerBox = configuration["AnswerBox"];

			ExpMinutes = (configuration["ExpMinutes"] ?? "120").GetInt();
			WorkId = (configuration["WorkId"] ?? "100").GetLong();
			PuMa = configuration["PuMa"];

			DBType.Name = _connection.DBType;
			if (string.IsNullOrEmpty(_connection.DbConnectionString))
				throw new System.Exception("未配置好数据库默认连接");

			try
			{
				var testConn = _connection.DbConnectionString.EncryptDES(Secret.DB);
				_connection.DbConnectionString = _connection.DbConnectionString.DecryptDES(Secret.DB);
			}
			catch { }

			if (!string.IsNullOrEmpty(_connection.RedisConnectionString))
			{
				try
				{
					_connection.RedisConnectionString = _connection.RedisConnectionString.DecryptDES(Secret.Redis);
				}
				catch { }
			}

		}
		// 多个节点name格式 ：["key:key1"]
		public static string GetSettingString(string key)
		{
			return Configuration[key];
		}
		// 多个节点,通过.GetSection("key")["key1"]获取
		public static IConfigurationSection GetSection(string key)
		{
			return Configuration.GetSection(key);
		}
	}

	public class Connection
	{
		public string DBType { get; set; }
		public string DbConnectionString { get; set; }
		public string RedisConnectionString { get; set; }
		public bool UseRedis { get; set; }
		public bool UseSignalR { get; set; }
	}

	public class CreateMember : TableDefaultColumns
	{
	}
	public class ModifyMember : TableDefaultColumns
	{
	}

	public abstract class TableDefaultColumns
	{
		public string UserIdField { get; set; }
		public string UserNameField { get; set; }
		public string DateField { get; set; }
	}
	public class GlobalFilter
	{
		public string Message { get; set; }
		public bool Enable { get; set; }
		public string[] Actions { get; set; }
	}

	public class HuaweiObs
	{
		public string SK { get; set; }
		
		public string AK { get; set; }

		public string Endpoint { get; set; }
	}

    public class Kafka
	{
		public bool UseProducer { get; set; }
		public ProducerSettings ProducerSettings { get; set; }
		public bool UseConsumer { get; set; }
		public bool IsConsumerSubscribe { get; set; }
		public ConsumerSettings ConsumerSettings { get; set; }
		public Topics Topics { get; set; }
	}
	public class ProducerSettings
	{
		public string BootstrapServers { get; set; }
		public string SaslMechanism { get; set; }
		public string SecurityProtocol { get; set; }
		public string SaslUsername { get; set; }
		public string SaslPassword { get; set; }
	}
	public class ConsumerSettings
	{
		public string BootstrapServers { get; set; }
		public string SaslMechanism { get; set; }
		public string SecurityProtocol { get; set; }
		public string SaslUsername { get; set; }
		public string SaslPassword { get; set; }
		public string GroupId { get; set; }
	}
	public class Topics
	{
		public string TestTopic { get; set; }
	}

	public class RabbitMQConfig
	{
		public string HostName { get; set; } = "localhost";
		public string UserName { get; set; } = "guest";
		public string Password { get; set; } = "guest";
		public int Port { get; set; } = 5672;
		public string VirtualHost { get; set; } = "/";
	}

	public class Secret
	{
		/// <summary>
		/// 用户密码加密key
		/// </summary>
		public string User { get; set; }
		/// <summary>
		/// 数据库加密key
		/// </summary>
		public string DB { get; set; }

		/// <summary>
		/// redis加密key
		/// </summary>
		public string Redis { get; set; }

		/// <summary>
		/// jwt加密key
		/// </summary>
		public string JWT { get; set; }

		public string Audience { get; set; }
		public string Issuer { get; set; }

		/// <summary>
		/// 导出文件加密key
		/// </summary>
		public string ExportFile = "C5ABA9E202D94C13A3CB66002BF77FAF";
	}

	public class Upload
	{
		public string OfficeToolUrl { get; set; }

		public string PuMaUrl { get; set; }

		public string WpsUploadUrl { get; set; }

		public string UwooWebSocket { get; set; }
	}

	public class WPSConfig
	{
		public string AppId { get; set; }

		public string AppSecretKey { get; set; }
	}
	public class PaperCenter
	{
		public string PaperCenterUrl { get; set; }
	}

    public class DataCenter
    {
        public string Domain { get; set; }

		public bool Enabled { get; set; }
    }

    public class CKDataCenter
    {
        public string Domain { get; set; }

        public bool Enabled { get; set; }
    }

    public class JPush
	{
		public string AppKey { get; set; }

		public string AppSecret { get; set; }
	}

	public class DouBaoAI
	{
		/// <summary>
		/// AK
		/// </summary>
		public string AccessKey { get; set; }

		/// <summary>
		/// Sk
		/// </summary>
		public string SecertAccessKey { get; set; }

		/// <summary>
		/// API Key
		/// </summary>
		public string APIKey { get; set; }

        /// <summary>
        /// AI生成图片模型Id
        /// </summary>
        public string AIGenerateImageModelId { get; set; }

		/// <summary>
		/// AI生成图片地址
		/// </summary>
        public string AIGenerateImageUrl { get; set; }

        /// <summary>
        /// AI生成HTML代码地址
        /// </summary>
        public string AIGenerateHTMLCodeUrl { get; set; }

        /// <summary>
        /// AI生成HTML代码模型Id
        /// </summary>
        public string AIGenerateHTMLCodeModeId { get; set; }

		public string AIGenerateQuestionsUrl { get; set; }

		/// <summary>
		/// 豆包知识库Api地址
		/// </summary>
		public string KnowLedgeBaseUrl { get; set; }

        /// <summary>
        /// 创建上下文缓存地址
        /// </summary>
        public string CreateContextUrl { get; set; }

		/// <summary>
		/// 上下文对话地址
		/// </summary>
        public string ContextDialogueUrl { get; set; }

		/// <summary>
		/// 语音技术APPId
		/// </summary>
        public string VoiceAppId { get; set; }

        /// <summary>
        /// 语音技术Token
        /// </summary>
        public string VoiceToken { get; set; }

        /// <summary>
        /// 语音文件转文本地址
        /// </summary>
        public string AudioFileChangeTextUrl { get; set; }

		/// <summary>
		/// 文本转语音地址
		/// </summary>
        public string TextChangeVoiceUrl { get; set; }

		/// <summary>
		/// 对话地址
		/// </summary>
        public string DialogueUrl { get; set; }

        /// <summary>
        /// 我的应用地址
        /// </summary>
        public string MyAppUrl { get; set; }

        /// <summary>
        /// 文件分析应用Id
        /// </summary>
        public string FileAnalysisAppId { get; set; }
    }

    public class OfficeTool
    {
        /// <summary>
        /// OfficeTool地址
        /// </summary>
        public string OfficeToolUrl { get; set; }

        /// <summary>
        /// Markdown文本转word地址
        /// </summary>
        public string MarkdownTextToWordUrl { get; set; }
    }
}
