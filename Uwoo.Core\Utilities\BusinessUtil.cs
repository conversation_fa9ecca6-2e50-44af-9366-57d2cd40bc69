﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace UwooAgent.Core.Utilities
{
    /// <summary>
    /// 业务工具
    /// </summary>
    public class BusinessUtil
    {
        /// <summary>
        /// 年级Id转名称
        /// </summary>
        /// <param name="gradeId"></param>
        /// <returns></returns>
        public static string GradeName(int gradeId)
        {
            switch (gradeId)
            {
                case 1:
                    return "一年级";
                case 2:
                    return "二年级";
                case 3:
                    return "三年级";
                case 4:
                    return "四年级";
                case 5:
                    return "五年级";
                case 6:
                    return "六年级";
                case 7:
                    return "七年级";
                case 8:
                    return "八年级";
                case 9:
                    return "九年级";
                case 10:
                    return "高一";
                case 11:
                    return "高二";
                case 12:
                    return "高三";
            }

            return "";
        }

        /// <summary>
        /// 年级Id转名称
        /// </summary>
        /// <param name="gradeId"></param>
        /// <returns></returns>
        public static string GradeName(string gradeId)
        {
            switch (gradeId)
            {
                case "1":
                    return "一年级";
                case "2":
                    return "二年级";
                case "3":
                    return "三年级";
                case "4":
                    return "四年级";
                case "5":
                    return "五年级";
                case "6":
                    return "六年级";
                case "7":
                    return "七年级";
                case "8":
                    return "八年级";
                case "9":
                    return "九年级";
                case "10":
                    return "高一";
                case "11":
                    return "高二";
                case "12":
                    return "高三";
            }

            return "";
        }

        /// <summary>
        /// 年级Id转阶段名称
        /// </summary>
        /// <param name="gradeId"></param>
        /// <returns></returns>
        public static string GetStageByGrade(int gradeId)
        {
            switch (gradeId)
            {
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                    return "小学";
                case 6:
                case 7:
                case 8:
                case 9:
                    return "初中";
                case 10:
                case 11:
                case 12:
                    return "高中";
            }
            return "未知阶段";
        }

        public static string ImgAttendEndCode(string imgStr)
        {
            var imgRegex =
                @"<img\b[^<>]*?\bsrc[\s\t\r\n]*=[\s\t\r\n]*[""']?[\s\t\r\n]*(?<imgUrl>[^\t\r\n""'<>]*)[^<>]*?/?[\s\t\r\n]*>";

            var ret = Regex.Replace(imgStr, imgRegex, (p) =>
            {
                var end = p.Value.LastIndexOf("/>") + 2;
                if (end == p.Value.Length)
                {
                    return p.Value.Replace("：", ":");
                }
                else
                {
                    var v = p.Value.Insert(p.Value.LastIndexOf('>'), "/");
                    return v.Replace("：", ":");
                }
            }, RegexOptions.IgnoreCase);

            return ret;
        }

        /// <summary>
        /// 获取百分比
        /// </summary>
        /// <param name="rightScore"></param>
        /// <param name="totalScore"></param>
        /// <returns></returns>
        public static decimal GetAccuracy(decimal rightScore, decimal totalScore, int decimals = 1)
        {
            if (rightScore == 0 || totalScore == 0)
            {
                return 0;
            }

            return Math.Round((rightScore / totalScore * 100), decimals);
        }
    }
}
