﻿using NetTaste;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI生成HTML代码豆包入参
    /// </summary>
    public class AIGenerateHTMLCodeDouBaoInput
    {
        /// <summary>
        /// 调用的模型的 ID
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 响应内容是否流式返回：
        /// false：模型生成完所有内容后一次性返回结果。
        /// true：按 SSE 协议逐块返回模型生成内容，并以一条 data: [DONE] 消息结束。当 stream 为 true 时，可设置 stream_options 字段以获取 token 用量统计信息。
        /// </summary>
        public bool? stream { get; set; }

        /// <summary>
        /// 流式响应的选项。当 stream 为 true 时，可设置 stream_options 字段。
        /// </summary>
        public AIGenerateHTMLCodeDouBaoStreamOptionsInput stream_options { get; set; } = new AIGenerateHTMLCodeDouBaoStreamOptionsInput();

        /// <summary>
        /// 消息列表
        /// </summary>
        public List<AIGenerateHTMLCodeDouBaoMessageInput> messages { get; set; } = new List<AIGenerateHTMLCodeDouBaoMessageInput>();

        /// <summary>
        /// 模型输出内容须遵循此处指定的格式
        /// </summary>
        public AIGenerateHTMLCodeDouBaoResponseFormatInput response_format { get; set; } = new AIGenerateHTMLCodeDouBaoResponseFormatInput();

        /// <summary>
        /// 控制模型是否开启深度思考模式。默认开启深度思考模式，可以手动关闭
        /// </summary>
        public AIGenerateHTMLCodeDouBaoThinkingInput thinking { get; set; } = new AIGenerateHTMLCodeDouBaoThinkingInput();
    }

    /// <summary>
    /// 流式响应的选项。当 stream 为 true 时，可设置 stream_options 字段。
    /// </summary>
    public class AIGenerateHTMLCodeDouBaoStreamOptionsInput
    {
        /// <summary>
        /// 模型流式输出时，是否在输出结束前输出本次请求的 token 用量信息。
        /// true：在 data: [DONE] 消息之前会返回一个额外的 chunk。此 chunk 中， usage 字段中输出整个请求的 token 用量，choices 字段为空数组。
        /// false：输出结束前，没有一个 chunk 来返回 token 用量信息。
        /// </summary>
        public bool include_usage { get; set; }

        /// <summary>
        /// 模型流式输出时，输出的每个 chunk 中是否输出本次请求到此 chunk 输出时刻的累计 token 用量信息。
        /// true：在返回的 usage 字段中，输出本次请求到此 chunk 输出时刻的累计 token 用量。
        /// false：不在每个 chunk 都返回 token 用量信息。
        /// </summary>
        public bool chunk_include_usage { get; set; }
    }

    /// <summary>
    /// 消息列表
    /// </summary>
    public class AIGenerateHTMLCodeDouBaoMessageInput
    {
        /// <summary>
        /// 发送消息的角色(system、user)
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 系统信息内容。
        /// </summary>
        public string? content { get; set; }
    }

    /// <summary>
    /// 模型输出内容须遵循此处指定的格式
    /// </summary>
    public class AIGenerateHTMLCodeDouBaoResponseFormatInput
    {
        /// <summary>
        /// 类型（text、json_object、json_schema）
        /// </summary>
        public string? type { get; set; }

        /// <summary>
        /// JSON结构体的定义。
        /// </summary>
        public AIGenerateHTMLCodeDouBaoJsonSchemaInput json_schema { get; set; } = new AIGenerateHTMLCodeDouBaoJsonSchemaInput();
    }

    /// <summary>
    /// JSON结构体的定义
    /// </summary>
    public class AIGenerateHTMLCodeDouBaoJsonSchemaInput
    {
        /// <summary>
        /// 用户自定义的JSON结构的名称。
        /// </summary>
        public string? name { get; set; }

        /// <summary>
        /// 回复格式的 JSON 格式定义，以 JSON Schema 对象的形式描述。
        /// </summary>
        public AIGenerateHTMLCodeDouBaoSchemaInput schema { get; set; } = new AIGenerateHTMLCodeDouBaoSchemaInput();

        /// <summary>
        /// 是否在生成输出时，启用严格遵循模式。
        /// true：模型将始终严格遵循schema字段中定义的格式。
        /// false：模型会尽可能遵循schema字段中定义的结构。
        /// </summary>
        public bool strict { get; set; }
    }

    /// <summary>
    /// 回复格式的 JSON 格式定义，以 JSON Schema 对象的形式描述
    /// </summary>
    public class AIGenerateHTMLCodeDouBaoSchemaInput
    {
        /// <summary>
        /// 工具名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// HTML代码
        /// </summary>
        public string? HtmlCode { get; set; }
    }

    /// <summary>
    /// 控制模型是否开启深度思考模式。默认开启深度思考模式，可以手动关闭
    /// </summary>
    public class AIGenerateHTMLCodeDouBaoThinkingInput
    {
        /// <summary>
        /// "disabled"：不使用深度思考能力。
        /// "enabled"：强制使用深度思考能力
        /// </summary>
        public string? type { get; set; }
    }
}
