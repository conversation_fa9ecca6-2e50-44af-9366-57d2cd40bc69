﻿using Azure;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model.AI;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_学生端项目化实践
    /// </summary>
    public class AgentStudentProjectService : ServiceBase<AI_AgentTask, IAgentStudentProjectRepository>, IAgentStudentProjectService, IDependency
    {
        #region DI

        private IAgentCommonService _agentCommonService;
        public AgentStudentProjectService(IAgentCommonService agentCommonService)
        {
            _agentCommonService = agentCommonService;
        }

        #endregion

        /// <summary>
        /// 获取学生端项目化实践信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<GetStudentProjectInfoOutput> GetStudentProjectInfo(GetStudentProjectInfoInput input)
        {
            try
            {
                //获取项目化实践任务基础信息
                GetStudentProjectInfoOutput projectInfoOutput = await DBSqlSugar.Queryable<AI_AgentTask>()
                    .Where(p => p.Id == input.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentProjectInfoOutput()
                    {
                        ProjectId = p.Id,
                        ProjectName = p.Name,
                        ProjectIntroduce = p.Introduce,
                        ProjectLogo = p.TaskLogo
                    })
                    .FirstAsync();
                if (projectInfoOutput == null)
                {
                    throw new BusException("项目化实践Id异常!");
                }

                //项目化实践阶段
                List<GetStudentProjectStageInfoOutput> projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentProjectStageInfoOutput()
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //项目化实践阶段任务
                List<GetStudentProjectStageTaskInfoOutput> projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .Select(p => new GetStudentProjectStageTaskInfoOutput()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        Name = p.Name,
                        Target = p.Target,
                        TaskType = p.TaskType,
                        GroupAssessmentScore = p.GroupAssessmentScore,
                        GroupIsAssessment = p.GroupIsAssessment,
                        GroupIsSubmit = p.GroupIsSubmit,
                        TaskAssessmentScore = p.TaskAssessmentScore,
                        TaskIsAssessment = p.TaskIsAssessment,
                        TaskIsSubmit = p.TaskIsSubmit,
                        Order = p.Order
                    })
                    .OrderBy(p => p.Order)
                    .ToListAsync();

                //获取学生做项目化实践任务信息
                List<AI_StudentDoProjectTask> studentDoProjectTasks = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>()
                    .Where(p => p.ProjectId == projectInfoOutput.ProjectId && p.IsDeleted == false)
                    .Select(p => new AI_StudentDoProjectTask()
                    {
                        Id = p.Id,
                        ProjectStageId = p.ProjectStageId,
                        ProjectStageTaskId = p.ProjectStageTaskId,
                        Score = p.Score,
                        IsStandard = p.IsStandard,
                        Order = p.Order
                    }).ToListAsync();

                //获取学生问答记录
                List<string> dialogueContentRecords = new List<string>();
                if (projectStageTasks.Count > 0)
                {
                    List<string> taskIds = projectStageTasks.Select(p => p.Id).ToList();
                    dialogueContentRecords = await DBSqlSugar.Queryable<AI_DialogueContentRecord>()
                        .Where(p => taskIds.Contains(p.BusinessId) && p.IsDeleted == false)
                        .Select(p => p.BusinessId).ToListAsync();
                }

                //下一个阶段是否锁
                bool isStages = false;
                //处理阶段信息
                foreach (var stage in projectStages)
                {
                    //获取当前阶段的任务
                    List<GetStudentProjectStageTaskInfoOutput> projectStageTask = projectStageTasks.Where(p => p.ProjectStageId == stage.Id).OrderBy(p => p.Order).ToList();
                    if (projectStageTask.Count <= 0)
                    {
                        stage.IsLock = false;
                        continue;
                    }

                    //设置当前阶段锁
                    stage.IsLock = isStages;

                    if (isStages)
                    {
                        //当前阶段下面的所有任务都锁
                        foreach (var item in projectStageTask)
                        {
                            item.IsLock = true;
                        }
                    }
                    else
                    {
                        //任务是否锁
                        bool isTask = false;
                        foreach (var task in projectStageTask)
                        {
                            //知识问答类型不锁
                            if (task.TaskType == 3)
                            {
                                //不锁
                                task.IsLock = false;
                                continue;
                            }

                            //设置当前任务锁
                            task.IsLock = isTask;

                            //验证下个任务是否锁
                            if (task.TaskIsSubmit || task.TaskIsAssessment)
                            {
                                //验证学生当前任务是否达标
                                if (studentDoProjectTasks.Where(p => p.ProjectStageTaskId == task.Id && p.IsStandard == true).FirstOrDefault() != null)
                                {
                                    //已经达标下个任务不锁
                                    isTask = false;
                                }
                                else
                                {
                                    //没有达标下个任务锁
                                    isTask = true;
                                }
                            }
                            else
                            {
                                //没有设置条件下个任务不锁
                                isTask = false;
                            }
                        }
                    }

                    //验证下一个阶段是否锁
                    foreach (var task in projectStageTask)
                    {
                        //获取学生做任务信息
                        List<AI_StudentDoProjectTask> studentDoProjectTask = studentDoProjectTasks.Where(p => p.ProjectStageTaskId == task.Id).ToList();
                        if (task.GroupIsSubmit)
                        {
                            if (studentDoProjectTask != null)
                            {
                                //已完成当前条件不锁
                                isStages = false;
                            }
                            else
                            {
                                //未完成当前条件锁
                                isStages = true;
                                break;
                            }
                        }
                        else
                        {
                            //未设置条件不锁
                            isStages = false;
                        }

                        if (task.GroupIsAssessment)
                        {
                            if (studentDoProjectTask.Max(p => p.Score) >= task.GroupAssessmentScore)
                            {
                                //已完成当前条件不锁
                                isStages = false;
                            }
                            else
                            {
                                //未完成当前条件锁
                                isStages = true;
                                break;
                            }
                        }
                        else
                        {
                            //未设置条件不锁
                            isStages = false;
                        }
                    }

                    //学生做任务状态
                    foreach (var task in projectStageTask)
                    {
                        //获取学生提交记录
                        List<AI_StudentDoProjectTask> doProjectTask = studentDoProjectTasks.Where(p => p.ProjectStageTaskId == task.Id).ToList();
                        if (doProjectTask.Count > 0)
                        {
                            //验证学生当前任务是否达标
                            if (doProjectTask.Where(p => p.IsStandard == true).FirstOrDefault() != null)
                            {
                                //任务达标已完成
                                task.State = 3;
                                //获取提交记录Id
                                task.TaskSubmitId = doProjectTask.Where(p => p.IsStandard == true).OrderBy(p => p.Order).FirstOrDefault()?.Id;
                            }
                            else
                            {
                                //是否存在问答记录
                                if (dialogueContentRecords.Contains(task.Id))
                                {
                                    //存在问答记录进行中
                                    task.State = 2;
                                }
                                else
                                {
                                    //不存在问答记录未开始
                                    task.State = 1;
                                }
                                //已提交但未达标
                                task.IsSubmitNoStandard = true;
                                //获取提交记录Id
                                task.TaskSubmitId = doProjectTask.Where(p => p.IsStandard == false).OrderByDescending(p => p.Order).FirstOrDefault()?.Id;
                            }
                        }
                        else
                        {
                            //未提交未达标
                            task.IsSubmitNoStandard = false;
                        }
                    }

                    stage.ProjectStageTaskInfos = projectStageTask;
                }

                projectInfoOutput.ProjectStageInfos = projectStages;
                projectInfoOutput.ProgressBar = BusinessUtil.GetAccuracy(studentDoProjectTasks.DistinctBy(p => p.ProjectStageTaskId).Count(p => p.IsStandard.Value), projectStageTasks.Count, 1);
                return projectInfoOutput;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }
    }
}
