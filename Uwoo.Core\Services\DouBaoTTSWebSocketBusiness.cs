﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Uwoo.Core.Services
{
	/// <summary>
	/// 豆包文本转语音WebSocket
	/// </summary>
	public class DouBaoTTSWebSocketBusiness
	{
		private const byte PROTOCOL_VERSION = 0b0001;
		private const byte DEFAULT_HEADER_SIZE = 0b0001;

		// Message Type:
		private const byte FULL_CLIENT_REQUEST = 0b0001;
		public const byte AUDIO_ONLY_RESPONSE = 0b1011;
		public const byte FULL_SERVER_RESPONSE = 0b1001;
		private const byte ERROR_INFORMATION = 0b1111;

		// Message Type Specific Flags
		private const byte MsgTypeFlagNoSeq = 0b0000; // Non-terminal packet with no sequence
		private const byte MsgTypeFlagPositiveSeq = 0b1;// Non-terminal packet with sequence > 0
		private const byte MsgTypeFlagLastNoSeq = 0b10;// last packet with no sequence
		private const byte MsgTypeFlagNegativeSeq = 0b11; // Payload contains event number (int32)
		private const byte MsgTypeFlagWithEvent = 0b100;
		// Message Serialization
		private const byte NO_SERIALIZATION = 0b0000;
		private const byte JSON = 0b0001;
		// Message Compression
		private const byte COMPRESSION_NO = 0b0000;
		private const byte COMPRESSION_GZIP = 0b0001;

		/// <summary>
		/// 默认事件,对于使用事件的方案，可以通过非0值来校验事件的合法性
		/// </summary>
		public const int EVENT_NONE = 0;

		/// <summary>
		/// 阶段申明创建连接（在 HTTP 建连 Upgrade 后）
		/// </summary>
		public const int EVENT_Start_Connection = 1;

		/// <summary>
		/// 结束连接
		/// </summary>
		public const int EVENT_FINISHED_Connection = 2;

		/// <summary>
		/// 成功建连
		/// </summary>
		public const int EVENT_ConnectionStarted = 50;

		/// <summary>
		/// 建连失败（可能是无法通过权限认证）
		/// </summary>
		public const int EVENT_ConnectionFailed = 51;

		/// <summary>
		/// 结束连接
		/// </summary>
		public const int EVENT_ConnectionFinished = 52;

		/// <summary>
		/// 阶段申明创建会话
		/// </summary>
		public const int EVENT_StartSession = 100;

		/// <summary>
		/// 声明结束会话
		/// </summary>
		public const int EVENT_FinishSession = 102;

		/// <summary>
		/// 成功开始会话
		/// </summary>
		public const int EVENT_SessionStarted = 150;

		/// <summary>
		/// 会话已结束
		/// </summary>
		public const int EVENT_SessionFinished = 152;

		/// <summary>
		/// 会话失败
		/// </summary>
		public const int EVENT_SessionFailed = 153;

		/// <summary>
		/// 传输请求内容
		/// </summary>
		public const int EVENT_TaskRequest = 200;

		/// <summary>
		/// TTS 返回句内容开始
		/// </summary>
		public const int EVENT_TTSSentenceStart = 350;

		/// <summary>
		/// TTS 返回句内容结束
		/// </summary>
		public const int EVENT_TTSSentenceEnd = 351;

		/// <summary>
		/// TTS 返回句的音频内容
		/// </summary>
		public const int EVENT_TTSResponse = 352;

		public class Header
		{
			public int protocol_version;
			public int header_size;
			public int message_type;
			public int message_type_specific_flags;
			public int serialization_method;
			public int message_compression;
			public int reserved;
		}

		public class Optional
		{
			public int size;
			public int eventType = EVENT_NONE;
			public string sessionId;

			public int errorCode;
			public int connectionSize;
			public string connectionId;

			public string response_meta_json;
			public int sequence;

			public bool IsEmpty()
			{
				return size == 0;
			}
		}

		/// <summary>
		/// TTS响应
		/// </summary>
		public class TTSResponse
		{
			public Header header;
			public Optional optional;
			public int payloadSize;
			public byte[] payload;
		}

		/// <summary>
		/// int转byte
		/// </summary>
		/// <param name="a"></param>
		/// <returns></returns>
		public static byte[] IntToBytes(int a)
		{
			return new byte[] {
			(byte)((a >> 24) & 0xFF),
			(byte)((a >> 16) & 0xFF),
			(byte)((a >> 8) & 0xFF),
			(byte)(a & 0xFF)
			};
		}

		/// <summary>
		/// byte转int
		/// </summary>
		/// <param name="src"></param>
		/// <returns></returns>
		/// <exception cref="ArgumentException"></exception>
		static int BytesToInt(byte[] src)
		{
			if (src == null || src.Length != 4)
			{
				throw new ArgumentException("byte array must be 4 bytes long");
			}
			return (src[0] << 24) | (src[1] << 16) | (src[2] << 8) | src[3];
		}

		public static byte[] GetHeader(byte messageType, byte messageTypeSpecificFlags, byte serialMethod, byte compressionType, byte reservedData)
		{
			return new byte[] {
			(byte)((PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE),
			(byte)((messageType << 4) | messageTypeSpecificFlags),
			(byte)((serialMethod << 4) | compressionType),
			reservedData};
		}

		public static void ReadConnectStarted(byte[] res, TTSResponse response, int start)
		{
			byte[] b = new byte[4];
			Array.Copy(res, start, b, 0, b.Length);
			start += b.Length;
			response.optional.size += b.Length;
			response.optional.connectionSize = BytesToInt(b);

			b = new byte[response.optional.connectionSize];
			Array.Copy(res, start, b, 0, b.Length);
			start += b.Length;
			response.optional.size += b.Length;
			response.optional.connectionId = Encoding.UTF8.GetString(b);
			ReadPayload(res, response, start);
		}

		public static void ReadConnectFailed(byte[] res, TTSResponse response, int start)
		{
			byte[] b = new byte[4];
			Array.Copy(res, start, b, 0, b.Length);
			response.optional.size += b.Length;
			start += b.Length;
			response.optional.connectionSize = BytesToInt(b);
			ReadMetaJson(res, response, start);
		}

		public static int ReadSequence(byte[] res, TTSResponse response, int start)
		{
			var header = response.header;
			var optional = response.optional;
			if (header.message_type_specific_flags == MsgTypeFlagNegativeSeq || header.message_type_specific_flags == MsgTypeFlagPositiveSeq)
			{
				byte[] temp = new byte[4];
				Array.Copy(res, start, temp, 0, temp.Length);
				optional.sequence = BytesToInt(temp);
				optional.size += temp.Length;
				return temp.Length;
			}
			return 0;
		}

		public static void ReadMetaJson(byte[] res, TTSResponse response, int start)
		{
			byte[] b = new byte[4];
			Array.Copy(res, start, b, 0, b.Length);
			start += b.Length;
			response.optional.size += b.Length;
			int size = BytesToInt(b);
			b = new byte[size];
			Array.Copy(res, start, b, 0, b.Length);
			response.optional.size += b.Length;
			response.optional.response_meta_json = Encoding.UTF8.GetString(b);
		}

		public static int ReadPayload(byte[] res, TTSResponse response, int start)
		{
			byte[] b = new byte[4];
			Array.Copy(res, start, b, 0, b.Length);
			start += b.Length;
			int size = BytesToInt(b);
			response.payloadSize += size;
			b = new byte[size];
			Array.Copy(res, start, b, 0, b.Length);
			response.payload = b;
			if (response.optional.eventType == FULL_SERVER_RESPONSE)
			{
				Console.WriteLine("===> payload:" + Encoding.UTF8.GetString(b));
			}
			return 4 + size;
		}

		public static int ReadErrorCode(byte[] res, TTSResponse response, int start)
		{
			byte[] b = new byte[4];
			Array.Copy(res, start, b, 0, b.Length);
			response.optional.errorCode = BytesToInt(b);
			response.optional.size += b.Length;
			return b.Length;
		}

		public static int ReadEvent(byte[] res, int masTypeFlag, TTSResponse response)
		{
			if (masTypeFlag == MsgTypeFlagWithEvent)
			{
				byte[] temp = new byte[4];
				Array.Copy(res, 4, temp, 0, temp.Length);
				int eventType = BytesToInt(temp);
				response.optional.eventType = eventType;
				response.optional.size += 4;
				return temp.Length;
			}
			return 0;
		}

		public static int ReadSessionId(byte[] res, TTSResponse response, int start)
		{
			byte[] b = new byte[4];
			Array.Copy(res, start, b, 0, b.Length);
			start += b.Length;
			int size = BytesToInt(b);
			byte[] sessionIdBytes = new byte[size];
			Array.Copy(res, start, sessionIdBytes, 0, sessionIdBytes.Length);
			response.optional.sessionId = Encoding.UTF8.GetString(sessionIdBytes);
			return b.Length + size;
		}

		/// <summary>
		/// 发送事件
		/// </summary>
		/// <param name="webSocket"></param>
		/// <param name="header"></param>
		/// <param name="eventType"></param>
		/// <param name="sessionId"></param>
		/// <param name="sequence"></param>
		/// <param name="payload"></param>
		/// <returns></returns>
		/// <exception cref="ArgumentNullException"></exception>
		public static async Task<bool> SendEvent(ClientWebSocket webSocket, byte[] header, int eventType, string sessionId, byte[] sequence, byte[] payload)
		{
			if (webSocket == null)
			{
				throw new ArgumentNullException(nameof(webSocket));
			}
			if (header == null)
			{
				throw new ArgumentNullException(nameof(header));
			}
			if (payload == null)
			{
				throw new ArgumentNullException(nameof(payload));
			}

			int full_client_request_size = header.Length;
			byte[]? eventBytes = null;
			if (eventType != EVENT_NONE)
			{
				eventBytes = IntToBytes(eventType);
				full_client_request_size += eventBytes.Length;
			}
			byte[]? sessionIdBytes = null;
			byte[]? sessionIdSize = null;
			if (sessionId != null)
			{
				sessionIdBytes = Encoding.UTF8.GetBytes(sessionId);
				sessionIdSize = IntToBytes(sessionIdBytes.Length);
				full_client_request_size += sessionIdBytes.Length;
				full_client_request_size += sessionIdSize.Length;
			}
			if (sequence != null)
			{
				full_client_request_size += sequence.Length;
			}

			full_client_request_size += payload.Length;
			byte[] payloadSize = IntToBytes(payload.Length);
			full_client_request_size += payloadSize.Length;

			byte[] full_client_request = new byte[full_client_request_size];

			int destPos = 0;
			Array.Copy(header, 0, full_client_request, destPos, header.Length);
			destPos += header.Length;
			if (eventBytes != null)
			{
				Array.Copy(eventBytes, 0, full_client_request, destPos, eventBytes.Length);
				destPos += eventBytes.Length;
			}
			if (sessionIdBytes != null)
			{
				Array.Copy(sessionIdSize, 0, full_client_request, destPos, sessionIdSize.Length);
				destPos += sessionIdSize.Length;
				Array.Copy(sessionIdBytes, 0, full_client_request, destPos, sessionIdBytes.Length);
				destPos += sessionIdBytes.Length;
			}
			if (sequence != null)
			{
				Array.Copy(sequence, 0, full_client_request, destPos, sequence.Length);
				destPos += sequence.Length;
			}
			Array.Copy(payloadSize, 0, full_client_request, destPos, payloadSize.Length);
			destPos += payloadSize.Length;
			Array.Copy(payload, 0, full_client_request, destPos, payload.Length);

			await webSocket.SendAsync(new ArraySegment<byte>(full_client_request), WebSocketMessageType.Binary, true, CancellationToken.None);
			return true;
		}

		/// <summary>
		/// 开始连接
		/// </summary>
		/// <param name="webSocket"></param>
		/// <returns></returns>
		public static async Task<bool> StartConnection(ClientWebSocket webSocket)
		{
			byte[] header = GetHeader(FULL_CLIENT_REQUEST, MsgTypeFlagWithEvent, JSON, COMPRESSION_NO, (byte)0);
			return await SendEvent(webSocket, header, EVENT_Start_Connection, null, null, Encoding.UTF8.GetBytes("{}"));
		}

		/// <summary>
		/// 开始会话
		/// </summary>
		/// <param name="webSocket"></param>
		/// <param name="sessionId"></param>
		/// <param name="speaker"></param>
		/// <returns></returns>
		public static async Task<bool> StartTTSSession(ClientWebSocket webSocket, string sessionId, string speaker)
		{
			JObject audio_params = new JObject();
			audio_params.Add("format", "pcm");
			audio_params.Add("sample_rate", 24000);

			JObject additions = new JObject();
			additions.Add("enable_latex_tn", true);
			additions.Add("enable_language_detector", true);
			additions.Add("disable_markdown_filter", true);
			additions.Add("max_length_to_filter_parenthesis", 100);

			JObject req_params = new JObject();
			req_params.Add("speaker", speaker);
			req_params.Add("audio_params", audio_params);
			req_params.Add("additions", JsonConvert.SerializeObject(additions));

			JObject payloadJObj = new JObject();
			payloadJObj.Add("event", EVENT_StartSession);
			payloadJObj.Add("req_params", req_params);

			byte[] header = GetHeader(FULL_CLIENT_REQUEST, MsgTypeFlagWithEvent, JSON, COMPRESSION_NO, (byte)0);
			return await SendEvent(webSocket, header, EVENT_StartSession, sessionId, null, Encoding.UTF8.GetBytes(payloadJObj.ToString()));
		}

		/// <summary>
		/// 发送信息
		/// </summary>
		/// <param name="webSocket"></param>
		/// <param name="speaker"></param>
		/// <param name="sessionId"></param>
		/// <param name="text"></param>
		/// <returns></returns>
		public static async Task<bool> SendTTSMessage(ClientWebSocket webSocket, string speaker, string sessionId, string text)
		{

			JObject additions = new JObject();
			additions.Add("enable_latex_tn", true);
			additions.Add("enable_language_detector", true);
			additions.Add("disable_markdown_filter", true);
			additions.Add("max_length_to_filter_parenthesis", 100);

			JObject audio_params = new JObject();
			audio_params.Add("format", "pcm");
			audio_params.Add("sample_rate", 24000);

			JObject req_params = new JObject();
			req_params.Add("text", text);
			req_params.Add("speaker", speaker);
			req_params.Add("audio_params", audio_params);
			req_params.Add("additions", JsonConvert.SerializeObject(additions));

			JObject payloadJObj = new JObject();
			payloadJObj.Add("event", EVENT_TaskRequest);
			payloadJObj.Add("req_params", req_params);

			byte[] header = GetHeader(FULL_CLIENT_REQUEST, MsgTypeFlagWithEvent, JSON, COMPRESSION_NO, (byte)0);
			return await SendEvent(webSocket, header, EVENT_TaskRequest, sessionId, null, Encoding.UTF8.GetBytes(payloadJObj.ToString()));
		}

		/// <summary>
		/// 解析响应包
		/// </summary>
		/// <param name="res"></param>
		/// <returns></returns>
		public static TTSResponse ParserResponse(byte[] res)
		{
			if (res == null || res.Length == 0)
			{
				return null;
			}
			var response = new TTSResponse();
			var header = new Header();
			response.header = header;

			const byte num = 0b00001111;
			header.protocol_version = (res[0] >> 4) & num;
			header.header_size = res[0] & 0x0f;
			header.message_type = (res[1] >> 4) & num;
			header.message_type_specific_flags = res[1] & 0x0f;
			header.serialization_method = res[2] >> num;
			header.message_compression = res[2] & 0x0f;
			header.reserved = res[3];

			int offset = 4;
			var optional = new Optional();
			response.optional = optional;

			if (header.message_type == FULL_SERVER_RESPONSE || header.message_type == AUDIO_ONLY_RESPONSE)
			{
				offset += ReadEvent(res, header.message_type_specific_flags, response);
				var eventType = response.optional.eventType;

				switch (eventType)
				{
					case EVENT_NONE:
						break;
					case EVENT_ConnectionStarted:
						ReadConnectStarted(res, response, offset);
						break;
					case EVENT_ConnectionFailed:
						ReadConnectFailed(res, response, offset);
						break;
					case EVENT_SessionStarted:
					case EVENT_SessionFailed:
					case EVENT_SessionFinished:
						offset += ReadSessionId(res, response, offset);
						ReadMetaJson(res, response, offset);
						break;
					default:
						offset += ReadSessionId(res, response, offset);
						offset += ReadSequence(res, response, offset);
						ReadPayload(res, response, offset);
						break;
				}
			}
			else if (header.message_type == ERROR_INFORMATION)
			{
				offset += ReadErrorCode(res, response, offset);
				ReadPayload(res, response, offset);
			}
			return response;
		}

		/// <summary>
		/// 结束会话
		/// </summary>
		/// <param name="webSocket"></param>
		/// <param name="sessionId"></param>
		/// <returns></returns>
		public static async Task<bool> FinishSession(ClientWebSocket webSocket, string sessionId)
		{
			byte[] header = GetHeader(FULL_CLIENT_REQUEST, MsgTypeFlagWithEvent, JSON, COMPRESSION_NO, (byte)0);
			return await SendEvent(webSocket, header, EVENT_FinishSession, sessionId, null, Encoding.UTF8.GetBytes("{}"));
		}

		/// <summary>
		/// 关闭连接
		/// </summary>
		/// <param name="webSocket"></param>
		/// <returns></returns>
		public static async Task<bool> FinishConnection(ClientWebSocket webSocket)
		{
			byte[] header = GetHeader(FULL_CLIENT_REQUEST, MsgTypeFlagWithEvent, JSON, COMPRESSION_NO, (byte)0);
			return await SendEvent(webSocket, header, EVENT_FINISHED_Connection, null, null, Encoding.UTF8.GetBytes("{}"));
		}
	}
}
