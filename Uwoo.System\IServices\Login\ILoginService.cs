﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Entity.DomainModels;
using Uwoo.Model.Login;

namespace Uwoo.System.IServices.Login
{
    public interface ILoginService : IService<Base_User>
    {
        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        SubmitLoginOutput SubmitLogin(SubmitLoginInput input);
    }
}
