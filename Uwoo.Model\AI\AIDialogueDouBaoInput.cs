﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// AI对话豆包入参
    /// </summary>
    public class AIDialogueDouBaoInput
    {
        /// <summary>
        /// 调用的模型的 ID
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 响应内容是否流式返回：
        /// false：模型生成完所有内容后一次性返回结果。
        /// true：按 SSE 协议逐块返回模型生成内容，并以一条 data: [DONE] 消息结束。当 stream 为 true 时，可设置 stream_options 字段以获取 token 用量统计信息。
        /// </summary>
        public bool? stream { get; set; }

        /// <summary>
        /// 流式响应的选项。当 stream 为 true 时，可设置 stream_options 字段。
        /// </summary>
        public AIDialogueDouBaoStreamOptionsInput stream_options { get; set; } = new AIDialogueDouBaoStreamOptionsInput();

        /// <summary>
        /// 消息列表
        /// </summary>
        public List<AIDialogueDouBaoMessageInput> messages { get; set; } = new List<AIDialogueDouBaoMessageInput>();

        /// <summary>
        /// 控制模型是否开启深度思考模式。默认开启深度思考模式，可以手动关闭
        /// </summary>
        public AIDialogueDouBaoThinkingInput thinking { get; set; } = new AIDialogueDouBaoThinkingInput();
    }

    /// <summary>
    /// 流式响应的选项。当 stream 为 true 时，可设置 stream_options 字段。
    /// </summary>
    public class AIDialogueDouBaoStreamOptionsInput
    {
        /// <summary>
        /// 模型流式输出时，是否在输出结束前输出本次请求的 token 用量信息。
        /// true：在 data: [DONE] 消息之前会返回一个额外的 chunk。此 chunk 中， usage 字段中输出整个请求的 token 用量，choices 字段为空数组。
        /// false：输出结束前，没有一个 chunk 来返回 token 用量信息。
        /// </summary>
        public bool include_usage { get; set; }

        /// <summary>
        /// 模型流式输出时，输出的每个 chunk 中是否输出本次请求到此 chunk 输出时刻的累计 token 用量信息。
        /// true：在返回的 usage 字段中，输出本次请求到此 chunk 输出时刻的累计 token 用量。
        /// false：不在每个 chunk 都返回 token 用量信息。
        /// </summary>
        public bool chunk_include_usage { get; set; }
    }

    /// <summary>
    /// 消息列表
    /// </summary>
    public class AIDialogueDouBaoMessageInput
    {
        /// <summary>
        /// 发送消息的角色(system、user)
        /// </summary>
        public string? role { get; set; }

        /// <summary>
        /// 系统信息内容。
        /// </summary>
        public string? content { get; set; }
    }

    /// <summary>
    /// 控制模型是否开启深度思考模式。默认开启深度思考模式，可以手动关闭
    /// </summary>
    public class AIDialogueDouBaoThinkingInput
    {
        /// <summary>
        /// "disabled"：不使用深度思考能力。
        /// "enabled"：强制使用深度思考能力
        /// </summary>
        public string? type { get; set; }
    }
}
