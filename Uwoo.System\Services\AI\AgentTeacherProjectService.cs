﻿using Coldairarrow.Util;
using MongoDB.Bson.Serialization.Serializers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Model.CustomException;
using Uwoo.System.IServices;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Entity.DomainModels.Base;
using UwooAgent.Entity.DomainModels.School;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.Model.SemesterTime;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_教师端项目化实践
    /// </summary>
    public class AgentTeacherProjectService : ServiceBase<AI_AgentTask, IAgentTeacherProjectRepository>, IAgentTeacherProjectService, IDependency
    {
        #region DI

        private IBase_SemesterTimeService _semesterTimeService;
        private IAgentCommonService _agentCommonService;
        public AgentTeacherProjectService(IBase_SemesterTimeService semesterTimeService,
            IAgentCommonService agentCommonService)
        {
            _semesterTimeService = semesterTimeService;
            _agentCommonService = agentCommonService;
        }

        #endregion

        /// <summary>
        /// 保存/编辑项目化实践任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task SaveProjectTaskInfo(SaveProjectTaskInfoInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Id))
                {
                    //获取学校信息
                    Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).With(SqlWith.NoLock).FirstAsync();
                    if (schoolInfo == null)
                    {
                        throw new BusException("学校Id异常!");
                    }

                    //获取当前学年学期
                    NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                    if (nowSemesterTime == null)
                    {
                        throw new BusException("无法获取当前学年!", 801);
                    }

                    //智能体任务
                    AI_AgentTask agentTask = new AI_AgentTask()
                    {
                        Id = IdHelper.GetId(),
                        AgentId = input.AgentId,
                        Name = input.Name,
                        Introduce = input.Introduce,
                        Term = nowSemesterTime.NowTerm,
                        Year = nowSemesterTime.Year,
                        TaskLogo = input.TaskLogo,
                        CreateTime = DateTime.Now,
                        Creator = input.TeacherId,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId,
                        IsDeleted = false,
                        SubjectId = input.SubjectId,
                        AgentTaskType = 2,
                        GradeId = input.GradeId
                    };
                    await DBSqlSugar.Insertable(agentTask).ExecuteCommandAsync();

                    //项目化实践阶段
                    List<AI_ProjectStage> addProjectStages = new List<AI_ProjectStage>();
                    //项目化实践阶段任务
                    List<AI_ProjectStageTask> addProjectStageTasks = new List<AI_ProjectStageTask>();
                    //项目化实践阶段任务高频问题
                    List<AI_ProjectStageTaskQuestion> taskQuestions = new List<AI_ProjectStageTaskQuestion>();
                    foreach (var projectStageInfo in input.ProjectStageInfos)
                    {
                        //项目化实践阶段
                        AI_ProjectStage projectStage = new AI_ProjectStage()
                        {
                            Id = IdHelper.GetId(),
                            ProjectId = agentTask.Id,
                            Name = projectStageInfo.Name,
                            Describe = projectStageInfo.Describe,
                            Order = input.ProjectStageInfos.IndexOf(projectStageInfo) + 1,
                            CreateTime = DateTime.Now,
                            Creator = input.TeacherId,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId,
                            IsDeleted = false
                        };
                        addProjectStages.Add(projectStage);

                        //项目化实践阶段任务处理
                        foreach (var projectStageTaskInfo in projectStageInfo.ProjectStageTaskInfos)
                        {
                            //项目化实践阶段任务
                            AI_ProjectStageTask projectStageTask = new AI_ProjectStageTask()
                            {
                                Id = IdHelper.GetId(),
                                ProjectStageId = projectStage.Id,
                                ProjectId = agentTask.Id,
                                TaskType = projectStageTaskInfo.TaskType,
                                Name = projectStageTaskInfo.Name,
                                Target = projectStageTaskInfo.Target,
                                ScoreStandard = projectStageTaskInfo.ScoreStandard,
                                Scope = projectStageTaskInfo.Scope,
                                RoleSetting = projectStageTaskInfo.RoleSetting,
                                Demand = projectStageTaskInfo.Demand,
                                GroupIsSubmit = projectStageTaskInfo.GroupIsSubmit,
                                GroupIsAssessment = projectStageTaskInfo.GroupIsAssessment,
                                GroupAssessmentScore = projectStageTaskInfo.GroupAssessmentScore,
                                TaskIsSubmit = projectStageTaskInfo.TaskIsSubmit,
                                TaskIsAssessment = projectStageTaskInfo.TaskIsAssessment,
                                TaskAssessmentScore = projectStageTaskInfo.TaskAssessmentScore,
                                Order = projectStageInfo.ProjectStageTaskInfos.IndexOf(projectStageTaskInfo) + 1,
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            };
                            addProjectStageTasks.Add(projectStageTask);

                            //项目化实践阶段任务高频问题
                            foreach (var questionInfo in projectStageTaskInfo.QuestionInfos)
                            {
                                taskQuestions.Add(new AI_ProjectStageTaskQuestion()
                                {
                                    Id = IdHelper.GetId(),
                                    ProjectStageTaskId = projectStageTask.Id,
                                    Name = questionInfo.Name,
                                    Describe = questionInfo.Describe,
                                    Order = projectStageTaskInfo.QuestionInfos.IndexOf(questionInfo) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                });
                            }
                        }
                    }

                    await DBSqlSugar.Insertable(addProjectStages).ExecuteCommandAsync();
                    await DBSqlSugar.Insertable(addProjectStageTasks).ExecuteCommandAsync();
                    await DBSqlSugar.Insertable(taskQuestions).ExecuteCommandAsync();
                    if (input.IsPublish)
                    {
                        //发布班级
                        List<AI_AgentTaskPublish> agentTaskPublishes = new List<AI_AgentTaskPublish>();
                        foreach (var item in input.ClassId)
                        {
                            agentTaskPublishes.Add(new AI_AgentTaskPublish()
                            {
                                Id = IdHelper.GetId(),
                                AgentTaskId = agentTask.Id,
                                PublishType = 1,
                                PublishBusinessId = item,
                                BeginTime = input.TimeRange[0],
                                EndTime = input.TimeRange[1],
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            });
                        }
                        await DBSqlSugar.Insertable(agentTaskPublishes).ExecuteCommandAsync();
                    }
                }
                else
                {
                    //获取项目化实践任务
                    AI_AgentTask agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.Id && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                    if (agentTask == null)
                    {
                        throw new BusException("项目化实践Id异常!");
                    }

                    //项目化实践阶段
                    List<AI_ProjectStage> projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>().Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false && p.Creator == input.TeacherId).ToListAsync();
                    //项目化实践阶段任务
                    List<AI_ProjectStageTask> projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false && p.Creator == input.TeacherId).ToListAsync();
                    //项目化实践阶段任务高频问题
                    List<AI_ProjectStageTaskQuestion> projectStageTaskQuestions = new List<AI_ProjectStageTaskQuestion>();
                    if (projectStageTasks.Count > 0)
                    {
                        projectStageTaskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>().Where(p => projectStageTasks.Select(p => p.Id).Contains(p.ProjectStageTaskId) && p.IsDeleted == false && p.Creator == input.TeacherId).ToListAsync();
                    }

                    //项目化实践发布记录
                    List<AI_AgentTaskPublish> taskPublishes = await DBSqlSugar.Queryable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id && p.IsDeleted == false && p.Creator == input.TeacherId).ToListAsync();

                    //更新状态(1都可编辑、2清空缓存池后都可以编辑、3只可以编辑没有答题数据的任务但不可以修改阶段和任务顺序)
                    int updateType = 0;
                    if (taskPublishes.Count <= 0)
                    {
                        //未发布状态下，所有信息都可编辑
                        updateType = 1;
                    }
                    else
                    {
                        if (projectStageTasks.Count <= 0)
                        {
                            //阶段任务为空，所有信息都可编辑
                            updateType = 1;
                        }
                        else
                        {
                            //验证是否存在问答数据
                            bool isExists = await DBSqlSugar.Queryable<AI_DialogueContentRecord>().AnyAsync(p => projectStageTasks.Select(p => p.Id).Contains(p.BusinessId) && p.IsDeleted == false);
                            if (!isExists)
                            {
                                //已发布，没有问答数据，可以随意编辑；
                                updateType = 1;
                            }
                            else
                            {
                                //验证是否存在提交数据
                                bool isDo = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>().AnyAsync(p => p.ProjectId == agentTask.Id && p.IsDeleted == false);
                                if (!isDo)
                                {
                                    //已发布，已有问答数据，但是没有提交数据,清空缓存池后都可以编辑；
                                    updateType = 2;
                                }
                                else
                                {
                                    //只可以编辑没有答题的任务但不可以修改阶段和任务顺序
                                    updateType = 3;
                                }
                            }
                        }
                    }

                    //智能体任务更新
                    agentTask.Name = input.Name;
                    agentTask.Introduce = input.Introduce;
                    agentTask.TaskLogo = input.TaskLogo;
                    agentTask.ModifyTime = DateTime.Now;
                    agentTask.Modifier = input.TeacherId;

                    await DBSqlSugar.Updateable(agentTask).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();

                    if (updateType == 1 || updateType == 2)
                    {
                        if (updateType == 2)
                        {
                            //清空问答记录和缓冲池
                            if (projectStageTasks.Count > 0)
                            {
                                await DBSqlSugar.Deleteable<AI_ContextCacheKey>().Where(p => projectStageTasks.Select(p => p.Id).Contains(p.BusinessId)).ExecuteCommandAsync();
                                await DBSqlSugar.Deleteable<AI_DialogueContentRecord>().Where(p => projectStageTasks.Select(p => p.Id).Contains(p.BusinessId)).ExecuteCommandAsync();
                            }
                        }

                        //项目化实践阶段
                        List<AI_ProjectStage> addProjectStages = new List<AI_ProjectStage>();
                        //项目化实践阶段任务
                        List<AI_ProjectStageTask> addProjectStageTasks = new List<AI_ProjectStageTask>();
                        //项目化实践阶段任务高频问题
                        List<AI_ProjectStageTaskQuestion> taskQuestions = new List<AI_ProjectStageTaskQuestion>();
                        foreach (var projectStageInfo in input.ProjectStageInfos)
                        {
                            //项目化实践阶段
                            AI_ProjectStage projectStage = new AI_ProjectStage()
                            {
                                Id = IdHelper.GetId(),
                                ProjectId = agentTask.Id,
                                Name = projectStageInfo.Name,
                                Describe = projectStageInfo.Describe,
                                Order = input.ProjectStageInfos.IndexOf(projectStageInfo) + 1,
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            };
                            addProjectStages.Add(projectStage);

                            //项目化实践阶段任务处理
                            foreach (var projectStageTaskInfo in projectStageInfo.ProjectStageTaskInfos)
                            {
                                //项目化实践阶段任务
                                AI_ProjectStageTask projectStageTask = new AI_ProjectStageTask()
                                {
                                    Id = IdHelper.GetId(),
                                    ProjectStageId = projectStage.Id,
                                    ProjectId = agentTask.Id,
                                    TaskType = projectStageTaskInfo.TaskType,
                                    Name = projectStageTaskInfo.Name,
                                    Target = projectStageTaskInfo.Target,
                                    ScoreStandard = projectStageTaskInfo.ScoreStandard,
                                    Scope = projectStageTaskInfo.Scope,
                                    RoleSetting = projectStageTaskInfo.RoleSetting,
                                    Demand = projectStageTaskInfo.Demand,
                                    GroupIsSubmit = projectStageTaskInfo.GroupIsSubmit,
                                    GroupIsAssessment = projectStageTaskInfo.GroupIsAssessment,
                                    GroupAssessmentScore = projectStageTaskInfo.GroupAssessmentScore,
                                    TaskIsSubmit = projectStageTaskInfo.TaskIsSubmit,
                                    TaskIsAssessment = projectStageTaskInfo.TaskIsAssessment,
                                    TaskAssessmentScore = projectStageTaskInfo.TaskAssessmentScore,
                                    Order = projectStageInfo.ProjectStageTaskInfos.IndexOf(projectStageTaskInfo) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                };
                                addProjectStageTasks.Add(projectStageTask);

                                //项目化实践阶段任务高频问题
                                foreach (var questionInfo in projectStageTaskInfo.QuestionInfos)
                                {
                                    taskQuestions.Add(new AI_ProjectStageTaskQuestion()
                                    {
                                        Id = IdHelper.GetId(),
                                        ProjectStageTaskId = projectStageTask.Id,
                                        Name = questionInfo.Name,
                                        Describe = questionInfo.Describe,
                                        Order = projectStageTaskInfo.QuestionInfos.IndexOf(questionInfo) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    });
                                }
                            }
                        }

                        //清空历史数据
                        if (projectStages.Count > 0)
                        {
                            await DBSqlSugar.Deleteable<AI_ProjectStage>().Where(p => projectStages.Select(p => p.Id).Contains(p.Id)).ExecuteCommandAsync();
                        }
                        if (projectStageTasks.Count > 0)
                        {
                            await DBSqlSugar.Deleteable<AI_ProjectStageTask>().Where(p => projectStageTasks.Select(p => p.Id).Contains(p.Id)).ExecuteCommandAsync();
                        }
                        if (projectStageTaskQuestions.Count > 0)
                        {
                            await DBSqlSugar.Deleteable<AI_ProjectStageTaskQuestion>().Where(p => projectStageTaskQuestions.Select(p => p.Id).Contains(p.Id)).ExecuteCommandAsync();
                        }

                        //保存最新数据
                        await DBSqlSugar.Insertable(addProjectStages).ExecuteCommandAsync();
                        await DBSqlSugar.Insertable(addProjectStageTasks).ExecuteCommandAsync();
                        await DBSqlSugar.Insertable(taskQuestions).ExecuteCommandAsync();
                    }
                    if (updateType == 3)
                    {
                        //项目化实践阶段(新增)
                        List<AI_ProjectStage> addProjectStages = new List<AI_ProjectStage>();
                        //项目化实践阶段任务(新增)
                        List<AI_ProjectStageTask> addProjectStageTasks = new List<AI_ProjectStageTask>();
                        //项目化实践阶段任务高频问题(新增)
                        List<AI_ProjectStageTaskQuestion> addTaskQuestions = new List<AI_ProjectStageTaskQuestion>();

                        //项目化实践阶段(修改)
                        List<AI_ProjectStage> updateProjectStages = new List<AI_ProjectStage>();
                        //项目化实践阶段任务(修改)
                        List<AI_ProjectStageTask> updateProjectStageTasks = new List<AI_ProjectStageTask>();

                        foreach (var projectStageInfo in input.ProjectStageInfos)
                        {
                            if (string.IsNullOrEmpty(projectStageInfo.Id))
                            {
                                //项目化实践阶段
                                AI_ProjectStage projectStage = new AI_ProjectStage()
                                {
                                    Id = IdHelper.GetId(),
                                    ProjectId = agentTask.Id,
                                    Name = projectStageInfo.Name,
                                    Describe = projectStageInfo.Describe,
                                    Order = input.ProjectStageInfos.IndexOf(projectStageInfo) + 1,
                                    CreateTime = DateTime.Now,
                                    Creator = input.TeacherId,
                                    ModifyTime = DateTime.Now,
                                    Modifier = input.TeacherId,
                                    IsDeleted = false
                                };
                                addProjectStages.Add(projectStage);

                                //项目化实践阶段任务处理
                                foreach (var projectStageTaskInfo in projectStageInfo.ProjectStageTaskInfos)
                                {
                                    //项目化实践阶段任务
                                    AI_ProjectStageTask projectStageTask = new AI_ProjectStageTask()
                                    {
                                        Id = IdHelper.GetId(),
                                        ProjectStageId = projectStage.Id,
                                        ProjectId = agentTask.Id,
                                        TaskType = projectStageTaskInfo.TaskType,
                                        Name = projectStageTaskInfo.Name,
                                        Target = projectStageTaskInfo.Target,
                                        ScoreStandard = projectStageTaskInfo.ScoreStandard,
                                        Scope = projectStageTaskInfo.Scope,
                                        RoleSetting = projectStageTaskInfo.RoleSetting,
                                        Demand = projectStageTaskInfo.Demand,
                                        GroupIsSubmit = projectStageTaskInfo.GroupIsSubmit,
                                        GroupIsAssessment = projectStageTaskInfo.GroupIsAssessment,
                                        GroupAssessmentScore = projectStageTaskInfo.GroupAssessmentScore,
                                        TaskIsSubmit = projectStageTaskInfo.TaskIsSubmit,
                                        TaskIsAssessment = projectStageTaskInfo.TaskIsAssessment,
                                        TaskAssessmentScore = projectStageTaskInfo.TaskAssessmentScore,
                                        Order = projectStageInfo.ProjectStageTaskInfos.IndexOf(projectStageTaskInfo) + 1,
                                        CreateTime = DateTime.Now,
                                        Creator = input.TeacherId,
                                        ModifyTime = DateTime.Now,
                                        Modifier = input.TeacherId,
                                        IsDeleted = false
                                    };
                                    addProjectStageTasks.Add(projectStageTask);

                                    //项目化实践阶段任务高频问题
                                    foreach (var questionInfo in projectStageTaskInfo.QuestionInfos)
                                    {
                                        addTaskQuestions.Add(new AI_ProjectStageTaskQuestion()
                                        {
                                            Id = IdHelper.GetId(),
                                            ProjectStageTaskId = projectStageTask.Id,
                                            Name = questionInfo.Name,
                                            Describe = questionInfo.Describe,
                                            Order = projectStageTaskInfo.QuestionInfos.IndexOf(questionInfo) + 1,
                                            CreateTime = DateTime.Now,
                                            Creator = input.TeacherId,
                                            ModifyTime = DateTime.Now,
                                            Modifier = input.TeacherId,
                                            IsDeleted = false
                                        });
                                    }
                                }
                            }
                            else
                            {
                                //修改项目化实践阶段
                                AI_ProjectStage projectStage = projectStages.Where(p => p.Id == projectStageInfo.Id).First();
                                if (projectStage != null)
                                {
                                    projectStage.Describe = projectStageInfo.Describe;
                                    projectStage.Name = projectStageInfo.Name;
                                    updateProjectStages.Add(projectStage);

                                    //修改项目化实践阶段任务
                                    foreach (var projectStageTaskInfo in projectStageInfo.ProjectStageTaskInfos)
                                    {
                                        if (string.IsNullOrEmpty(projectStageTaskInfo.Id))
                                        {
                                            //项目化实践阶段任务
                                            AI_ProjectStageTask projectStageTask = new AI_ProjectStageTask()
                                            {
                                                Id = IdHelper.GetId(),
                                                ProjectStageId = projectStage.Id,
                                                ProjectId = agentTask.Id,
                                                TaskType = projectStageTaskInfo.TaskType,
                                                Name = projectStageTaskInfo.Name,
                                                Target = projectStageTaskInfo.Target,
                                                ScoreStandard = projectStageTaskInfo.ScoreStandard,
                                                Scope = projectStageTaskInfo.Scope,
                                                RoleSetting = projectStageTaskInfo.RoleSetting,
                                                Demand = projectStageTaskInfo.Demand,
                                                GroupIsSubmit = projectStageTaskInfo.GroupIsSubmit,
                                                GroupIsAssessment = projectStageTaskInfo.GroupIsAssessment,
                                                GroupAssessmentScore = projectStageTaskInfo.GroupAssessmentScore,
                                                TaskIsSubmit = projectStageTaskInfo.TaskIsSubmit,
                                                TaskIsAssessment = projectStageTaskInfo.TaskIsAssessment,
                                                TaskAssessmentScore = projectStageTaskInfo.TaskAssessmentScore,
                                                Order = projectStageInfo.ProjectStageTaskInfos.IndexOf(projectStageTaskInfo) + 1,
                                                CreateTime = DateTime.Now,
                                                Creator = input.TeacherId,
                                                ModifyTime = DateTime.Now,
                                                Modifier = input.TeacherId,
                                                IsDeleted = false
                                            };
                                            addProjectStageTasks.Add(projectStageTask);

                                            //项目化实践阶段任务高频问题
                                            foreach (var questionInfo in projectStageTaskInfo.QuestionInfos)
                                            {
                                                addTaskQuestions.Add(new AI_ProjectStageTaskQuestion()
                                                {
                                                    Id = IdHelper.GetId(),
                                                    ProjectStageTaskId = projectStageTask.Id,
                                                    Name = questionInfo.Name,
                                                    Describe = questionInfo.Describe,
                                                    Order = projectStageTaskInfo.QuestionInfos.IndexOf(questionInfo) + 1,
                                                    CreateTime = DateTime.Now,
                                                    Creator = input.TeacherId,
                                                    ModifyTime = DateTime.Now,
                                                    Modifier = input.TeacherId,
                                                    IsDeleted = false
                                                });
                                            }
                                        }
                                        else
                                        {
                                            //获取项目化实践任务
                                            AI_ProjectStageTask projectStageTask = projectStageTasks.Where(p => p.Id == projectStageTaskInfo.Id).First();
                                            if (projectStageTask != null)
                                            {
                                                //验证是否存在答题记录
                                                bool isDo = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>().AnyAsync(p => p.ProjectStageTaskId == projectStageTask.Id && p.IsDeleted == false);
                                                if (!isDo)
                                                {
                                                    //清空缓冲池和问答记录
                                                    await DBSqlSugar.Deleteable<AI_ContextCacheKey>().Where(p => p.BusinessId == projectStageTask.Id).ExecuteCommandAsync();
                                                    await DBSqlSugar.Deleteable<AI_DialogueContentRecord>().Where(p => p.BusinessId == projectStageTask.Id).ExecuteCommandAsync();

                                                    //项目化实践阶段任务
                                                    projectStageTask.TaskType = projectStageTaskInfo.TaskType;
                                                    projectStageTask.Name = projectStageTaskInfo.Name;
                                                    projectStageTask.Target = projectStageTaskInfo.Target;
                                                    projectStageTask.ScoreStandard = projectStageTaskInfo.ScoreStandard;
                                                    projectStageTask.Scope = projectStageTaskInfo.Scope;
                                                    projectStageTask.RoleSetting = projectStageTaskInfo.RoleSetting;
                                                    projectStageTask.Demand = projectStageTaskInfo.Demand;
                                                    projectStageTask.GroupIsSubmit = projectStageTaskInfo.GroupIsSubmit;
                                                    projectStageTask.GroupIsAssessment = projectStageTaskInfo.GroupIsAssessment;
                                                    projectStageTask.GroupAssessmentScore = projectStageTaskInfo.GroupAssessmentScore;
                                                    projectStageTask.TaskIsSubmit = projectStageTaskInfo.TaskIsSubmit;
                                                    projectStageTask.TaskIsAssessment = projectStageTaskInfo.TaskIsAssessment;
                                                    projectStageTask.TaskAssessmentScore = projectStageTaskInfo.TaskAssessmentScore;
                                                    projectStageTask.ModifyTime = DateTime.Now;
                                                    projectStageTask.Modifier = input.TeacherId;
                                                    updateProjectStageTasks.Add(projectStageTask);

                                                    //清空缓冲池和问答记录
                                                    await DBSqlSugar.Deleteable<AI_ProjectStageTaskQuestion>().Where(p => p.ProjectStageTaskId == projectStageTask.Id).ExecuteCommandAsync();
                                                    //项目化实践阶段任务高频问题
                                                    foreach (var questionInfo in projectStageTaskInfo.QuestionInfos)
                                                    {
                                                        addTaskQuestions.Add(new AI_ProjectStageTaskQuestion()
                                                        {
                                                            Id = IdHelper.GetId(),
                                                            ProjectStageTaskId = projectStageTask.Id,
                                                            Name = questionInfo.Name,
                                                            Describe = questionInfo.Describe,
                                                            Order = projectStageTaskInfo.QuestionInfos.IndexOf(questionInfo) + 1,
                                                            CreateTime = DateTime.Now,
                                                            Creator = input.TeacherId,
                                                            ModifyTime = DateTime.Now,
                                                            Modifier = input.TeacherId,
                                                            IsDeleted = false
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (addProjectStages.Count > 0)
                        {
                            await DBSqlSugar.Insertable(addProjectStages).ExecuteCommandAsync();
                        }
                        if (addProjectStageTasks.Count > 0)
                        {
                            await DBSqlSugar.Insertable(addProjectStageTasks).ExecuteCommandAsync();
                        }
                        if (addTaskQuestions.Count > 0)
                        {
                            await DBSqlSugar.Insertable(addTaskQuestions).ExecuteCommandAsync();
                        }
                        if (updateProjectStages.Count > 0)
                        {
                            await DBSqlSugar.Updateable(updateProjectStages).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        }
                        if (updateProjectStageTasks.Count > 0)
                        {
                            await DBSqlSugar.Updateable(updateProjectStageTasks).WhereColumns(it => new { it.Id }).ExecuteCommandAsync();
                        }
                    }

                    if (input.IsPublish)
                    {
                        //发布班级
                        List<AI_AgentTaskPublish> agentTaskPublishes = new List<AI_AgentTaskPublish>();
                        foreach (var item in input.ClassId)
                        {
                            agentTaskPublishes.Add(new AI_AgentTaskPublish()
                            {
                                Id = IdHelper.GetId(),
                                AgentTaskId = agentTask.Id,
                                PublishType = 1,
                                PublishBusinessId = item,
                                BeginTime = input.TimeRange[0],
                                EndTime = input.TimeRange[1],
                                CreateTime = DateTime.Now,
                                Creator = input.TeacherId,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId,
                                IsDeleted = false
                            });
                        }
                        //清空历史数据
                        await DBSqlSugar.Deleteable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id).ExecuteCommandAsync();
                        //保存最新数据
                        await DBSqlSugar.Insertable(agentTaskPublishes).ExecuteCommandAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取项目化实践详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ProjectTaskDetailsOutput> GetProjectTaskDetails(ProjectTaskDetailsInput input)
        {
            try
            {
                // 查询项目化实践任务基本信息
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("项目化实践任务不存在或无权限访问!");
                }

                // 初始化输出对象
                var output = new ProjectTaskDetailsOutput
                {
                    Id = agentTask.Id,
                    Name = agentTask.Name,
                    Introduce = agentTask.Introduce,
                    TaskLogo = agentTask.TaskLogo,
                    TeacherId = agentTask.Creator
                };

                // 查询项目化实践阶段
                var projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>().Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false).OrderBy(p => p.Order).ToListAsync();
                if (projectStages.Any())
                {
                    output.ProjectStageInfos = new List<ProjectStageDetailsOutput>();

                    // 查询项目化实践阶段任务
                    var projectStageIds = projectStages.Select(p => p.Id).ToList();
                    var projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => projectStageIds.Contains(p.ProjectStageId) && p.IsDeleted == false).OrderBy(p => p.Order).ToListAsync();

                    if (projectStageTasks.Any())
                    {
                        // 查询项目化实践阶段任务高频问题
                        var projectStageTaskIds = projectStageTasks.Select(p => p.Id).ToList();
                        var taskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>().Where(p => projectStageTaskIds.Contains(p.ProjectStageTaskId) && p.IsDeleted == false).OrderBy(p => p.Order).ToListAsync();

                        // 查询任务是否有答题记录
                        var taskHasAnswers = await DBSqlSugar.Queryable<AI_StudentDoProjectTask>()
                            .Where(p => projectStageTaskIds.Contains(p.ProjectStageTaskId) && p.IsDeleted == false)
                            .GroupBy(p => p.ProjectStageTaskId)
                            .Select(p => p.ProjectStageTaskId)
                            .ToListAsync();

                        // 构建阶段输出
                        foreach (var stage in projectStages)
                        {
                            var stageOutput = new ProjectStageDetailsOutput
                            {
                                Id = stage.Id,
                                Name = stage.Name,
                                Describe = stage.Describe,
                                ProjectStageTaskInfos = new List<ProjectStageTaskDetailsOutput>()
                            };

                            // 构建阶段任务输出
                            var stageTasks = projectStageTasks.Where(p => p.ProjectStageId == stage.Id).ToList();
                            foreach (var task in stageTasks)
                            {
                                var taskOutput = new ProjectStageTaskDetailsOutput
                                {
                                    Id = task.Id,
                                    TaskType = task.TaskType,
                                    Name = task.Name,
                                    Target = task.Target,
                                    ScoreStandard = task.ScoreStandard,
                                    Demand = task.Demand,
                                    Scope = task.Scope,
                                    RoleSetting = task.RoleSetting,
                                    GroupIsSubmit = task.GroupIsSubmit,
                                    GroupIsAssessment = task.GroupIsAssessment,
                                    GroupAssessmentScore = task.GroupAssessmentScore,
                                    TaskIsSubmit = task.TaskIsSubmit,
                                    TaskIsAssessment = task.TaskIsAssessment,
                                    TaskAssessmentScore = task.TaskAssessmentScore,
                                    QuestionInfos = taskQuestions
                                        .Where(q => q.ProjectStageTaskId == task.Id)
                                        .Select(q => new ProjectStageTaskQuestionDetailsOutput
                                        {
                                            Id = q.Id,
                                            Name = q.Name,
                                            Describe = q.Describe
                                        })
                                        .ToList(),
                                    IsDo = taskHasAnswers.Contains(task.Id)
                                };

                                stageOutput.ProjectStageTaskInfos.Add(taskOutput);
                            }

                            output.ProjectStageInfos.Add(stageOutput);
                        }
                    }
                }

                //获取项目化实践发布的班级
                List<AI_AgentTaskPublish> taskPublishes = await DBSqlSugar.Queryable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == output.Id && p.IsDeleted == false && p.Creator == input.TeacherId).ToListAsync();
                if (taskPublishes.Count > 0)
                {
                    //发布时间范围(下标0开始、下标1结束)
                    List<DateTime?> timeRange = new List<DateTime?>() { taskPublishes[0].BeginTime, taskPublishes[0].EndTime };
                    output.TimeRange = timeRange;

                    //发布的班级
                    output.ClassId = taskPublishes.Select(p => p.PublishBusinessId).ToList();
                }

                return output;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 删除项目化实践任务
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task DelProjectTask(DelProjectTaskInput input)
        {
            try
            {
                // 查询项目化实践任务
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("项目化实践任务不存在或无权限访问!");
                }

                // 查询项目化实践阶段
                var projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>().Where(p => p.ProjectId == agentTask.Id && p.IsDeleted == false).ToListAsync();
                if (projectStages.Any())
                {
                    // 查询项目化实践阶段任务
                    var projectStageIds = projectStages.Select(p => p.Id).ToList();
                    var projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => projectStageIds.Contains(p.ProjectStageId) && p.IsDeleted == false).ToListAsync();
                    if (projectStageTasks.Any())
                    {
                        // 查询项目化实践阶段任务高频问题
                        var projectStageTaskIds = projectStageTasks.Select(p => p.Id).ToList();
                        var taskQuestions = await DBSqlSugar.Queryable<AI_ProjectStageTaskQuestion>().Where(p => projectStageTaskIds.Contains(p.ProjectStageTaskId) && p.IsDeleted == false).ToListAsync();
                        // 逻辑删除高频问题
                        if (taskQuestions.Any())
                        {
                            var updateQuestions = taskQuestions.Select(q => new AI_ProjectStageTaskQuestion
                            {
                                Id = q.Id,
                                IsDeleted = true,
                                ModifyTime = DateTime.Now,
                                Modifier = input.TeacherId
                            }).ToList();
                            await DBSqlSugar.Updateable(updateQuestions).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
                        }

                        // 逻辑删除阶段任务
                        var updateTasks = projectStageTasks.Select(t => new AI_ProjectStageTask
                        {
                            Id = t.Id,
                            IsDeleted = true,
                            ModifyTime = DateTime.Now,
                            Modifier = input.TeacherId
                        }).ToList();

                        await DBSqlSugar.Updateable(updateTasks).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
                    }

                    // 逻辑删除阶段
                    var updateStages = projectStages.Select(s => new AI_ProjectStage
                    {
                        Id = s.Id,
                        IsDeleted = true,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId
                    }).ToList();
                    await DBSqlSugar.Updateable(updateStages).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
                }

                // 逻辑删除发布记录
                var taskPublishes = await DBSqlSugar.Queryable<AI_AgentTaskPublish>().Where(p => p.AgentTaskId == agentTask.Id && p.IsDeleted == false).ToListAsync();
                if (taskPublishes.Any())
                {
                    var updatePublishes = taskPublishes.Select(p => new AI_AgentTaskPublish
                    {
                        Id = p.Id,
                        IsDeleted = true,
                        ModifyTime = DateTime.Now,
                        Modifier = input.TeacherId
                    }).ToList();
                    await DBSqlSugar.Updateable(updatePublishes).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
                }

                // 逻辑删除智能体任务
                agentTask.IsDeleted = true;
                agentTask.ModifyTime = DateTime.Now;
                agentTask.Modifier = input.TeacherId;
                await DBSqlSugar.Updateable(agentTask).UpdateColumns(it => new { it.IsDeleted, it.ModifyTime, it.Modifier }).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 发布项目化实践任务到班级
        /// </summary>
        /// <param name="input">发布参数</param>
        /// <returns></returns>
        public async Task PublishProjectTaskToClasses(PublishProjectTaskToClassInput input)
        {
            try
            {
                // 检查任务是否存在且属于当前教师
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("项目化实践任务不存在或无权限访问!");
                }

                // 检查项目是否有有效的阶段和任务
                var projectStages = await DBSqlSugar.Queryable<AI_ProjectStage>().Where(p => p.ProjectId == input.ProjectId && p.IsDeleted == false).ToListAsync();
                if (!projectStages.Any())
                {
                    throw new BusException("项目至少需要一个有效阶段才能发布!");
                }

                var projectStageIds = projectStages.Select(p => p.Id).ToList();
                var projectStageTasks = await DBSqlSugar.Queryable<AI_ProjectStageTask>().Where(p => projectStageIds.Contains(p.ProjectStageId) && p.IsDeleted == false).ToListAsync();
                if (!projectStageTasks.Any())
                {
                    throw new BusException("项目至少需要一个有效任务才能发布!");
                }

                // 逻辑删除该任务之前的所有发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.ProjectId && p.Creator == input.TeacherId).ExecuteCommandAsync();

                // 创建新的发布记录
                var publishRecords = input.ClassIds.Select(classId => new AI_AgentTaskPublish
                {
                    Id = IdHelper.GetId(),
                    AgentTaskId = input.ProjectId,
                    PublishType = 1, // 班级发布
                    PublishBusinessId = classId,
                    BeginTime = input.TimeRange[0].Value,
                    EndTime = input.TimeRange[1].Value,
                    CreateTime = DateTime.Now,
                    Creator = input.TeacherId,
                    ModifyTime = DateTime.Now,
                    Modifier = input.TeacherId,
                    IsDeleted = false
                }).ToList();

                // 批量插入发布记录
                await DBSqlSugar.Insertable(publishRecords).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 撤销项目化实践任务发布
        /// </summary>
        /// <param name="input">撤销发布参数</param>
        /// <returns></returns>
        public async Task UnpublishProjectTask(UnpublishProjectTaskInput input)
        {
            try
            {
                // 检查任务是否存在且属于当前教师
                var agentTask = await DBSqlSugar.Queryable<AI_AgentTask>().Where(p => p.Id == input.ProjectId && p.IsDeleted == false && p.Creator == input.TeacherId).FirstAsync();
                if (agentTask == null)
                {
                    throw new BusException("项目化实践任务不存在或无权限访问!");
                }

                // 逻辑删除该任务的所有发布记录
                await DBSqlSugar.Updateable<AI_AgentTaskPublish>()
                    .SetColumns(p => new AI_AgentTaskPublish { IsDeleted = true, ModifyTime = DateTime.Now, Modifier = input.TeacherId })
                    .Where(p => p.AgentTaskId == input.ProjectId && p.Creator == input.TeacherId).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 获取项目化实践列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageReturn<GetProjectTaskListOutput>> GetProjectTaskList(GetProjectTaskListInput input)
        {
            try
            {
                //获取学校信息
                Exam_School schoolInfo = await DBSqlSugar.Queryable<Exam_School>().Where(p => p.Id == input.SchoolId).FirstAsync();
                if (schoolInfo == null)
                {
                    throw new BusException("学校Id异常!");
                }

                //获取当前学年学期
                NowSemesterTime nowSemesterTime = await _semesterTimeService.GetNowYearSemesterTime();
                if (nowSemesterTime == null)
                {
                    throw new BusException("无法获取当前学年!", 801);
                }

                //参数
                List<SugarParameter> parameters = new List<SugarParameter>();
                parameters.Add(new SugarParameter("@year", nowSemesterTime.Year));
                parameters.Add(new SugarParameter("@term", nowSemesterTime.NowTerm));
                parameters.Add(new SugarParameter("@teacherId", input.TeacherId));
                parameters.Add(new SugarParameter("@agentId", input.AgentId));
                parameters.Add(new SugarParameter("@subjectId", input.SubjectId));
                parameters.Add(new SugarParameter("@gradeId", input.GradeId));
                string where = string.Empty;
                string whereTwo = string.Empty;
                if (!string.IsNullOrEmpty(input.Name))
                {
                    where += $" AND t.Name LIKE '%{input.Name}%' ";
                }

                if (!string.IsNullOrEmpty(input.ClassId))
                {
                    where += $" AND atp.PublishBusinessId=@classId  ";
                    parameters.Add(new SugarParameter("@classId", input.ClassId));
                }

                if (input.PublishStatus != 0)
                {
                    whereTwo += $" AND PublishStatus=@publishStatus ";
                    parameters.Add(new SugarParameter("@publishStatus", input.PublishStatus));
                }

                //获取项目化实践列表
                string sql = $@"SELECT
                                	AgentId,
                                	ProjectId,
                                	Name,
                                	Introduce,
                                	CreateTime,
                                	TaskLogo,
                                	PublishStatus 
                                FROM
                                	(
                                	SELECT
                                		t.AgentId,
                                		t.Id AS ProjectId,
                                		t.Name,
                                		t.Introduce,
                                		t.CreateTime,
                                		COALESCE ( t.TaskLogo, a.Logo ) AS TaskLogo,
                                	    CASE WHEN atp.AgentTaskId IS NOT NULL THEN 1 ELSE 2 END AS PublishStatus,
                                		ROW_NUMBER ( ) OVER ( PARTITION BY t.Id ORDER BY atp.CreateTime DESC ) AS rn 
                                	FROM
                                		AI_AgentTask t WITH ( NOLOCK )
                                		INNER JOIN AI_AgentBaseInfo a WITH ( NOLOCK ) ON t.AgentId = a.Id 
                                		AND a.IsDeleted = 0
                                		LEFT JOIN AI_AgentTaskPublish atp WITH ( NOLOCK ) ON t.Id = atp.AgentTaskId 
                                		AND atp.IsDeleted = 0 
                                	WHERE
                                		t.IsDeleted = 0 
                                		AND t.[Year] = @year 
                                		AND t.Term = @term 
                                		AND t.SubjectId = @subjectId 
                                		AND t.AgentId =@agentId 
                                		AND t.Creator = @teacherId 
                                		AND t.GradeId = @gradeId
                                		AND t.AgentTaskType = 2 {where}
                                	) AS sub 
                                WHERE
                                	rn = 1 {whereTwo}";
                RefAsync<int> totalNumber = 0;
                List<GetProjectTaskListOutput> taskListOutputs = await DBSqlSugar.SqlQueryable<GetProjectTaskListOutput>(sql)
                    .AddParameters(parameters)
                    .OrderBy("CreateTime DESC,PublishStatus DESC")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                if (taskListOutputs.Count > 0)
                {
                    List<string> ids = taskListOutputs.Select(p => p.ProjectId).ToList();

                    //获取智能体任务发布的班级
                    string classSql = $@"SELECT
                                         	atp.AgentTaskId,
                                            class.Id AS ClassId,
                                         	class.ClassName 
                                         FROM
                                         	AI_AgentTaskPublish atp WITH ( NOLOCK )
                                         	INNER JOIN Exam_Class class WITH ( NOLOCK ) ON atp.PublishBusinessId= class.Id 
                                         WHERE
                                         	atp.IsDeleted= 0 
                                         	AND atp.Creator=@teacherId 
                                            AND atp.AgentTaskId in (@agentTaskIdIds) ";
                    List<AgentTeacherOralCommunicationListClass> classInfos = await DBSqlSugar.SqlQueryable<AgentTeacherOralCommunicationListClass>(classSql)
                        .AddParameters(new { teacherId = input.TeacherId, agentTaskIdIds = ids }).ToListAsync();
                    foreach (var taskListOutput in taskListOutputs)
                    {
                        taskListOutput.ClassInfos = classInfos.Where(p => p.AgentTaskId == taskListOutput.ProjectId)
                            .Select(p => new GetProjectTaskListClassInfoOutput()
                            {
                                ClassId = p.ClassId,
                                ClassName = p.ClassName,
                            }).ToList();
                    }
                }

                return new PageReturn<GetProjectTaskListOutput>()
                {
                    Datas = taskListOutputs,
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// AI创建项目化实践阶段任务相关属性
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task AICreateTaskProperty(AICreateTaskPropertyInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string model = await DBSqlSugar.Queryable<AI_ModelConfig>().Where(p => p.SceneType == 5).Select(p => p.Model).FirstAsync();
                string url = AppSetting.DouBaoAI.DialogueUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(model) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //年级名称
                string gradeName = BusinessUtil.GradeName(input.GradeId);

                //获取学科
                Exam_Subject subjectInfo = await DBSqlSugar.Queryable<Exam_Subject>().Where(p => p.Id == input.SubjectId).With(SqlWith.NoLock).FirstAsync();

                //指令处理
                string directiveStr = string.Empty;
                if (input.TaskType == 1)
                {
                    //获取成果评估类型指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherProjectAICreateOutcomeProperty_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取项目化实践阶段成果评估系统提示词,请联系管理员!");
                    }
                    directiveStr = directive?.Directive;
                }
                if (input.TaskType == 2)
                {
                    //获取情景对话类型指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherProjectAICreateDialogueProperty_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取项目化实践阶段情景对话系统提示词,请联系管理员!");
                    }
                    directiveStr = directive?.Directive;
                }
                if (input.TaskType == 3)
                {
                    //获取知识问答类型指令
                    AI_Directive directive = await DBSqlSugar.Queryable<AI_Directive>().Where(p => p.Key == AIAgentKeys.TeacherProjectAICreateKeyknowledgeProperty_Directive && p.IsDeleted == false).With(SqlWith.NoLock).FirstAsync();
                    if (directive == null)
                    {
                        throw new BusException("无法获取项目化实践阶段知识问答系统提示词,请联系管理员!");
                    }
                    directiveStr = directive?.Directive;
                }
                if (string.IsNullOrEmpty(directiveStr))
                {
                    throw new BusException("无法获取系统提示词,请联系管理员!");
                }
                directiveStr = directiveStr.Replace("{任务名称}", input.Name)
                .Replace("{任务目标}", input.Target)
                .Replace("{学段}", gradeName)
                .Replace("{学科}", subjectInfo?.Name);

                //入参处理
                List<AICreateTaskPropertyDouBaoMessageInput> messages = new List<AICreateTaskPropertyDouBaoMessageInput>()
                {
                    new AICreateTaskPropertyDouBaoMessageInput(){  role="system", content=directiveStr},
                    new AICreateTaskPropertyDouBaoMessageInput(){ role="user", content=$"请帮我生成相关数据" }
                };
                AICreateTaskPropertyDouBaoInput douBaoInput = new AICreateTaskPropertyDouBaoInput()
                {
                    model = model,
                    messages = messages,
                    stream = true,
                    stream_options = new AICreateTaskPropertyDouBaoStreamOptionsInput()
                    {
                        include_usage = true
                    },
                    response_format = new AICreateTaskPropertyDouBaoResponseFormatInput()
                    {
                        type = "json_schema",
                        json_schema = new AICreateTaskPropertyDouBaoJsonSchemaInput()
                        {
                            name = "PropertyGenerator",
                            strict = true,
                            schema = new AICreateTaskPropertyOutput()
                            {
                                Demand = "对话要求（注意:300字以内）",
                                RoleSetting = "评估角色设定（注意:300字以内）",
                                ScoreStandard = "评分标准（注意:100字以内）",
                                Scope = "问答范围（注意:300字以内）",
                                QuestionInfos = new List<AICreateTaskPropertyQuestionOutput>()
                        {
                            new AICreateTaskPropertyQuestionOutput()
                            {
                                Name="主题（注意:10字以内,最多输出5个）",
                                Describe="描述（注意:50字以内）"
                            }
                        }
                            }
                        }
                    },
                    thinking = new AICreateTaskPropertyDouBaoThinkingInput()
                    {
                        type = "disabled"
                    }
                };
                string jsonData = JsonConvert.SerializeObject(douBaoInput);

                // 创建一个用于累积JSON片段的缓冲区
                string jsonBuffer = string.Empty;
                //http请求
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            using (var stream = await response.Content.ReadAsStreamAsync())
                            {
                                using (var reader = new StreamReader(stream, Encoding.UTF8))
                                {
                                    string line;
                                    while ((line = await reader.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
                                    {
                                        if (line.StartsWith("data: [DONE]"))
                                        {
                                            break;
                                        }
                                        // 处理SSE数据行
                                        if (line.StartsWith("data:"))
                                        {
                                            // 去除(data: )进行解析数据
                                            var data = line.Substring("data:".Length).Trim();
                                            if (!string.IsNullOrEmpty(data))
                                            {
                                                AICreateTaskPropertyDouBaoOutput douBaoOutput = JsonConvert.DeserializeObject<AICreateTaskPropertyDouBaoOutput>(data);
                                                if (douBaoOutput != null && douBaoOutput.choices.Count > 0 && douBaoOutput.choices[0].delta != null)
                                                {
                                                    // 将新数据添加到缓冲区
                                                    jsonBuffer += douBaoOutput.choices[0].delta.content;
                                                    // 尝试解析完整的属性
                                                    bool propertyFound = true;

                                                    while (propertyFound && !cancellationToken.IsCancellationRequested)
                                                    {
                                                        propertyFound = TryParseNextProperty(jsonBuffer, out string? propertyName, out JToken? propertyValue, out int parsedLength);

                                                        if (propertyFound && propertyName != null && propertyValue != null)
                                                        {
                                                            // 创建一个只包含当前属性的输出对象
                                                            var propertyOutput = new AICreateTaskPropertyOutput();

                                                            switch (propertyName)
                                                            {
                                                                case "ScoreStandard":
                                                                    propertyOutput.ScoreStandard = propertyValue.ToString();
                                                                    break;
                                                                case "Demand":
                                                                    propertyOutput.Demand = propertyValue.ToString();
                                                                    break;
                                                                case "Scope":
                                                                    propertyOutput.Scope = propertyValue.ToString();
                                                                    break;
                                                                case "RoleSetting":
                                                                    propertyOutput.RoleSetting = propertyValue.ToString();
                                                                    break;
                                                                case "QuestionInfos":
                                                                    // 特殊处理数组属性
                                                                    propertyOutput.QuestionInfos = propertyValue.ToObject<List<AICreateTaskPropertyQuestionOutput>>();
                                                                    Console.WriteLine($"解析到QuestionInfos，包含 {propertyOutput.QuestionInfos.Count} 个问题");
                                                                    break;
                                                            }

                                                            // 立即推送给前端
                                                            AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                                                            {
                                                                Success = true,
                                                                Content = JsonConvert.SerializeObject(propertyOutput),
                                                                BusinessId = propertyName
                                                            };
                                                            string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                                                            await dataHandler(ssePushData);

                                                            // 从缓冲区中移除已处理的数据
                                                            jsonBuffer = jsonBuffer.Substring(parsedLength);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            string errorMsg = await response.Content.ReadAsStringAsync();
                            AgentSSEOutput errorSSEOutput = new AgentSSEOutput()
                            {
                                Success = false,
                                Content = $"第三方接口异常：{response.StatusCode}，详情：{errorMsg}"
                            };
                            //SSE推送客户端
                            string errorPushData = "data: " + JsonConvert.SerializeObject(errorSSEOutput) + "\n\n";
                            await dataHandler(errorPushData);
                        }
                    }
                }

                //执行结束
                AgentSSEOutput endSSEOutput = new AgentSSEOutput()
                {
                    Success = true,
                    Content = "DONE"
                };
                //SSE推送客户端
                string endPushData = "data: " + JsonConvert.SerializeObject(endSSEOutput) + "\n\n";
                await dataHandler(endPushData);
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 尝试解析下一个完整的属性
        /// </summary>
        /// <param name="jsonBuffer"></param>
        /// <param name="propertyName"></param>
        /// <param name="propertyValue"></param>
        /// <param name="parsedLength"></param>
        /// <returns></returns>
        private bool TryParseNextProperty(string jsonBuffer, out string? propertyName, out JToken? propertyValue, out int parsedLength)
        {
            propertyName = null;
            propertyValue = null;
            parsedLength = 0;

            // 寻找属性名称的开始（格式："属性名":）
            int nameStartIndex = jsonBuffer.IndexOf('"');
            if (nameStartIndex < 0)
                return false;

            int nameEndIndex = jsonBuffer.IndexOf('"', nameStartIndex + 1);
            if (nameEndIndex < 0)
                return false;

            // 提取属性名称
            propertyName = jsonBuffer.Substring(nameStartIndex + 1, nameEndIndex - nameStartIndex - 1);

            // 寻找属性值的开始（冒号后面的字符）
            int valueStartIndex = jsonBuffer.IndexOf(':', nameEndIndex);
            if (valueStartIndex < 0)
                return false;

            valueStartIndex++; // 跳到冒号后面的位置

            // 跳过空白字符
            while (valueStartIndex < jsonBuffer.Length && char.IsWhiteSpace(jsonBuffer[valueStartIndex]))
                valueStartIndex++;

            if (valueStartIndex >= jsonBuffer.Length)
                return false;

            // 根据值的类型找到结束位置
            int valueEndIndex = FindValueEnd(jsonBuffer, valueStartIndex);
            if (valueEndIndex < 0)
                return false;

            try
            {
                // 提取属性值的JSON字符串
                string valueJson = jsonBuffer.Substring(valueStartIndex, valueEndIndex - valueStartIndex + 1);

                // 解析属性值
                propertyValue = JToken.Parse(valueJson);

                // 计算已解析的长度（包括属性名、冒号和值）
                parsedLength = valueEndIndex + 1;

                // 如果后面有逗号，也包含逗号
                if (parsedLength < jsonBuffer.Length && jsonBuffer[parsedLength] == ',')
                    parsedLength++;

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 找到JSON值的结束位置（考虑不同类型的值和嵌套情况）
        /// </summary>
        /// <param name="jsonBuffer"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        private int FindValueEnd(string jsonBuffer, int startIndex)
        {
            char firstChar = jsonBuffer[startIndex];

            switch (firstChar)
            {
                case '{': // 对象
                    return FindJsonEnd(jsonBuffer, startIndex);

                case '[': // 数组
                    int depth = 0;
                    bool inString = false;
                    char stringDelimiter = '\0';

                    for (int i = startIndex; i < jsonBuffer.Length; i++)
                    {
                        char c = jsonBuffer[i];

                        // 处理字符串
                        if (c == '"' || c == '\'')
                        {
                            // 检查是否是转义字符
                            if (i > 0 && jsonBuffer[i - 1] == '\\')
                                continue;

                            if (!inString)
                            {
                                inString = true;
                                stringDelimiter = c;
                            }
                            else if (c == stringDelimiter)
                            {
                                inString = false;
                            }
                        }

                        if (inString)
                            continue;

                        // 处理数组结构字符
                        if (c == '[')
                            depth++;
                        else if (c == ']')
                            depth--;

                        // 如果找到匹配的结束括号
                        if (depth == 0 && c == ']')
                            return i;
                    }
                    return -1;

                case '"': // 字符串
                    for (int i = startIndex + 1; i < jsonBuffer.Length; i++)
                    {
                        char c = jsonBuffer[i];

                        // 检查是否是转义字符
                        if (c == '"' && i > 0 && jsonBuffer[i - 1] != '\\')
                            return i;
                    }
                    return -1;

                default: // 数字、布尔值、null
                         // 找到值的结束（逗号、右括号等）
                    for (int i = startIndex; i < jsonBuffer.Length; i++)
                    {
                        char c = jsonBuffer[i];
                        if (c == ',' || c == '}' || c == ']')
                            return i - 1;
                    }
                    return jsonBuffer.Length - 1;
            }
        }

        /// <summary>
        /// 找到JSON对象的结束位置（考虑嵌套情况）
        /// </summary>
        /// <param name="jsonBuffer"></param>
        /// <param name="startIndex"></param>
        /// <returns></returns>
        private int FindJsonEnd(string jsonBuffer, int startIndex)
        {
            int depth = 0;
            bool inString = false;
            char stringDelimiter = '\0';

            for (int i = startIndex; i < jsonBuffer.Length; i++)
            {
                char c = jsonBuffer[i];

                // 处理字符串
                if (c == '"' || c == '\'')
                {
                    // 检查是否是转义字符
                    if (i > 0 && jsonBuffer[i - 1] == '\\')
                        continue;

                    if (!inString)
                    {
                        inString = true;
                        stringDelimiter = c;
                    }
                    else if (c == stringDelimiter)
                    {
                        inString = false;
                    }
                }

                if (inString)
                    continue;

                // 处理JSON结构字符
                if (c == '{')
                    depth++;
                else if (c == '}')
                    depth--;

                // 如果找到匹配的结束括号
                if (depth == 0 && c == '}')
                    return i;
            }

            return -1; // 没有找到完整的JSON对象
        }
    }
}
