对象名 'Sys_Log' 无效。   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean isAsync, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String method)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteReader()
   at SqlSugar.SqlDataAdapter.Fill(DataSet ds)
   at SqlSugar.AdoProvider.GetDataSetAll(String sql, SugarParameter[] parameters)
   at SqlSugar.AdoProvider.GetDataTable(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.ToDataTable()
   at SqlSugar.FastestProvider`1.<ToDdateTable>b__41_0()
   at SqlSugar.ReflectionInoCore`1.GetOrCreate(String cacheKey, Func`1 create)
   at SqlSugar.FastestProvider`1.ToDdateTable(List`1 datas)
   at SqlSugar.FastestProvider`1._BulkCopy(List`1 datas)
   at SqlSugar.FastestProvider`1.BulkCopyAsync(List`1 datas)
   at SqlSugar.FastestProvider`1.BulkCopy(List`1 datas)
   at Uwoo.Core.Services.Logger.Start() in D:\Van\Vue.NetCore-master\Net6.SqlSugar\Uwoo.Core\Services\Logger.cs:line 183Core Microsoft SqlClient Data Provider
