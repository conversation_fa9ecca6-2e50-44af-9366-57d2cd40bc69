﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 教师获取学生口语交际评估结果输出
    /// </summary>
    public class GetOralCommunicationStudentResultOutput
    {
        /// <summary>
        /// 智能体Logo
        /// </summary>
        public string? Logo { get; set; }

        /// <summary>
        /// 智能体任务名称
        /// </summary>
        public string? AgentTaskName { get; set; }

        /// <summary>
        /// 智能体任务项目背景介绍
        /// </summary>
        public string? AgentTaskIntroduce { get; set; }

        /// <summary>
        /// 学生Id
        /// </summary>
        public string? StudentId { get; set; }

        /// <summary>
        /// 学生头像
        /// </summary>
        public string? StudentLogo { get; set; }

        /// <summary>
        /// 学生姓名
        /// </summary>
        public string? StudentName { get; set; }

        /// <summary>
        /// 学生评估结果
        /// </summary>
        public string? AssessmentResult { get; set; }

        /// <summary>
        /// 互动模式
        /// </summary>
        public string? InteractiveMode { get; set; }
    }
}
