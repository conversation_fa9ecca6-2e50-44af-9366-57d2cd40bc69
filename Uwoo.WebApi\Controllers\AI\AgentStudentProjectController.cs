﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_学生端项目化实践
    /// </summary>
    [Route("/AgentStudentProject/[controller]/[action]")]
    [ApiController]
    public class AgentStudentProjectController : ApiBaseController<IAgentStudentProjectService>
    {
        #region DI
        private readonly IAgentStudentProjectService _agentStudentProjectService;
        public AgentStudentProjectController(IAgentStudentProjectService agentStudentProjectService)
        {
            _agentStudentProjectService = agentStudentProjectService;
        }
        #endregion

        /// <summary>
        /// 获取学生端项目化实践信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<GetStudentProjectInfoOutput> GetStudentProjectInfo(GetStudentProjectInfoInput input)
        {
            if (string.IsNullOrEmpty(input.ProjectId))
            {
                throw new BusException("项目化实践任务Id不能为空!");
            }

            if (string.IsNullOrEmpty(input.StudentId))
            {
                throw new BusException("教师Id不能为空!");
            }
            return await _agentStudentProjectService.GetStudentProjectInfo(input);
        }
    }
}
