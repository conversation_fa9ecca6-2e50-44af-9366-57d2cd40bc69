﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Model.AI
{
    /// <summary>
    /// 智能体_教师端口语交际输入
    /// </summary>
    public class AgentTeacherOralCommunicationListInput
    {
        /// <summary>
        /// 页码（默认1）
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页数量（默认10）
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 教师Id
        /// </summary>
        public string? TeacherId { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string? SchoolId { get; set; }

        /// <summary>
        /// 智能体Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 班级Id
        /// </summary>
        public string? ClassId { get; set; }

        /// <summary>
        /// 互动模式(0全部、1指令式、2对话式、3辩论式)
        /// </summary>
        public int? InteractiveMode { get; set; }

        /// <summary>
        /// 学科Id
        /// </summary>
        public string? SubjectId { get; set; }

        /// <summary>
        /// 年级Id
        /// </summary>
        public string? GradeId { get; set; }
    }
}
