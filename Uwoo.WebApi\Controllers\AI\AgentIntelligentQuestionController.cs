using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Uwoo.Core.Controllers.Basic;
using Uwoo.Model.CustomException;
using Uwoo.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IServices.AI;
using Castle.Core.Internal;

namespace UwooAgent.WebApi.Controllers.AI
{
    /// <summary>
    /// 智能体_智能出题
    /// </summary>
    [Route("/AgentIntelligentQuestion/[controller]/[action]")]
    [ApiController]
    public class AgentIntelligentQuestionController : ApiBaseController<IAgentIntelligentQuestionService>
    {
        #region DI
        private readonly IAgentIntelligentQuestionService _agentIntelligentQuestionService;
        public AgentIntelligentQuestionController(IAgentIntelligentQuestionService agentIntelligentQuestionService)
        {
            _agentIntelligentQuestionService = agentIntelligentQuestionService;
        }
        #endregion

        /// <summary>
        /// 获取题型列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<QuestionTypeDto> GetQuestionTypes()
        {
            return _agentIntelligentQuestionService.GetQuestionTypes();
        }

        /// <summary>
        /// 获取难度等级列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<DifficultyLevelDto> GetDifficultyLevels()
        {
            return _agentIntelligentQuestionService.GetDifficultyLevels();
        }

        /// <summary>
        /// 获取出题方向列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public List<QuestionDirectionDto> GetQuestionDirections()
        {
            return _agentIntelligentQuestionService.GetQuestionDirections();
        }

        /// <summary>
        /// 根据学科获取知识点列表
        /// </summary>
        /// <param name="subjectId">学科ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<List<KnowledgePointDto>> GetKnowledgePointsBySubject(string subjectId)
        {
            if (string.IsNullOrEmpty(subjectId))
            {
                throw new BusException("学科ID不能为空!", 400);
            }

            return await _agentIntelligentQuestionService.GetKnowledgePointsBySubject(subjectId);
        }

        /// <summary>
        /// 智能出题
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResult<string>> GenerateQuestions(IntelligentQuestionGenerationInput input)
        {
            try
            {
                // 参数验证
                if (input.QuestionTypeIds == null || input.QuestionTypeIds.Count == 0)
                {
                    throw new BusException("请选择题型!", 801);
                }

                // DifficultyLevelName 和 QuestionDirectionName 改为非必填，如果为空将使用默认值

                if (input.QuestionCount <= 0 || input.QuestionCount > 30)
                {
                    throw new BusException("出题数量必须在1-30之间!", 801);
                }

                input.SubjectId = SubjectId;
                input.UserId = UserId;

                // ConnectionId 为可选参数，用于实时推送功能
                // 如果不需要实时推送，可以不传此参数

                // 根据出题方式验证特定参数
                switch (input.Mode)
                {
                    case QuestionGenerationMode.KnowledgePoint:
                        if (input.KnowledgePointIds == null || input.KnowledgePointIds.Count == 0)
                        {
                            throw new BusException("请选择知识点!", 801);
                        }
                        break;

                    case QuestionGenerationMode.Text:
                        if (string.IsNullOrEmpty(input.TextContent))
                        {
                            throw new BusException("请输入出题范围文本!", 801);
                        }
                        if (input.TextContent.Length > 500)
                        {
                            throw new BusException("文本内容不能超过500字符!", 801);
                        }
                        break;

                    case QuestionGenerationMode.Attachment:
                        // 支持新的多文件格式和旧的单文件格式
                        bool hasFiles = false;
                        if (input.FileUrls != null && input.FileUrls.Count > 0)
                        {
                            // 新格式：多文件支持
                            if (input.FileUrls.Count > 3)
                            {
                                throw new BusException("最多支持上传3个附件文件!", 801);
                            }
                            // 检查是否有空的URL
                            if (input.FileUrls.Any(url => string.IsNullOrEmpty(url)))
                            {
                                throw new BusException("附件文件URL不能为空!", 801);
                            }
                            hasFiles = true;
                        }
                        else if (!input.FileUrl.IsNullOrEmpty())
                        {
                            // 旧格式：单文件兼容
                            hasFiles = true;
                        }

                        if (!hasFiles)
                        {
                            throw new BusException("请上传附件文件!", 801);
                        }
                        break;

                    case QuestionGenerationMode.Chapter:
                        // 支持新的多章节格式和旧的单章节格式
                        bool hasChapters = false;
                        if (input.ChapterIds != null && input.ChapterIds.Count > 0)
                        {
                            // 新格式：多章节支持
                            // 检查是否有空的章节ID
                            if (input.ChapterIds.Any(id => string.IsNullOrEmpty(id)))
                            {
                                throw new BusException("章节ID不能为空!", 801);
                            }
                            hasChapters = true;
                        }
                        else if (!string.IsNullOrEmpty(input.ChapterName))
                        {
                            // 旧格式：单章节兼容
                            hasChapters = true;
                        }

                        if (!hasChapters)
                        {
                            throw new BusException("请选择章节!", 801);
                        }
                        break;

                    default:
                        throw new BusException("出题方式参数错误!", 801);
                }

                // 调用服务开始生成题目
                var taskId = await _agentIntelligentQuestionService.GenerateQuestionsAsync(input);

                return new ApiResult<string>
                {
                    code = 200,
                    message = "开始生成题目",
                    data = taskId
                };
            }
            catch (BusException ex)
            {
                return new ApiResult<string>
                {
                    code = ex.ErrorCode,
                    message = ex.Message,
                    data = null
                };
            }
            catch (Exception ex)
            {
                return new ApiResult<string>
                {
                    code = 500,
                    message = "系统异常: " + ex.Message,
                    data = null
                };
            }
        }

        /// <summary>
        /// 智能出题（SSE流式返回）
        /// </summary>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task GenerateQuestionsStream(IntelligentQuestionGenerationInput input, CancellationToken cancellationToken)
        {
            try
            {
                // 参数验证
                if (input.QuestionCount <= 0 || input.QuestionCount > 30)
                {
                    throw new Exception("题目数量必须在1-30之间!");
                }

                if (input.QuestionTypeIds == null || input.QuestionTypeIds.Count == 0)
                {
                    throw new Exception("请选择题型!");
                }

                // DifficultyLevelName 和 QuestionDirectionName 改为非必填，如果为空将使用默认值

                input.SubjectId = SubjectId;
                input.UserId = UserId;

                // 根据出题方式验证特定参数
                switch (input.Mode)
                {
                    case QuestionGenerationMode.KnowledgePoint:
                        if (input.KnowledgePointIds == null || input.KnowledgePointIds.Count == 0)
                        {
                            throw new Exception("请选择知识点!");
                        }
                        break;

                    case QuestionGenerationMode.Text:
                        if (string.IsNullOrEmpty(input.TextContent))
                        {
                            throw new Exception("请输入出题范围文本!");
                        }
                        if (input.TextContent.Length > 500)
                        {
                            throw new Exception("文本内容不能超过500字符!");
                        }
                        break;

                    case QuestionGenerationMode.Attachment:
                        // 支持新的多文件格式和旧的单文件格式
                        bool hasFiles = false;
                        if (input.FileUrls != null && input.FileUrls.Count > 0)
                        {
                            // 新格式：多文件支持
                            if (input.FileUrls.Count > 3)
                            {
                                throw new Exception("最多支持上传3个附件文件!");
                            }
                            // 检查是否有空的URL
                            if (input.FileUrls.Any(url => string.IsNullOrEmpty(url)))
                            {
                                throw new Exception("附件文件URL不能为空!");
                            }
                            hasFiles = true;
                        }
                        else if (!string.IsNullOrEmpty(input.FileUrl))
                        {
                            // 旧格式：单文件兼容
                            hasFiles = true;
                        }

                        if (!hasFiles)
                        {
                            throw new Exception("请上传附件文件!");
                        }
                        break;

                    case QuestionGenerationMode.Chapter:
                        // 支持新的多章节格式和旧的单章节格式
                        bool hasChapters = false;
                        if (input.ChapterIds != null && input.ChapterIds.Count > 0)
                        {
                            // 新格式：多章节支持
                            // 检查是否有空的章节ID
                            if (input.ChapterIds.Any(id => string.IsNullOrEmpty(id)))
                            {
                                throw new Exception("章节ID不能为空!");
                            }
                            hasChapters = true;
                        }
                        else if (!string.IsNullOrEmpty(input.ChapterName))
                        {
                            // 旧格式：单章节兼容
                            hasChapters = true;
                        }

                        if (!hasChapters)
                        {
                            throw new Exception("请选择章节!");
                        }
                        break;

                    default:
                        throw new Exception("出题方式参数错误!");
                }

                // 设置SSE
                Response.Headers.Append("Content-Type", "text/event-stream");
                Response.Headers.Append("Cache-Control", "no-cache");
                Response.Headers.Append("Connection", "keep-alive");


                await _agentIntelligentQuestionService.GenerateQuestionsStream(input, async (data) =>
                {
                    if (cancellationToken.IsCancellationRequested)
                        return;

                    await Response.WriteAsync(data, cancellationToken);
                    await Response.Body.FlushAsync(cancellationToken);
                }, cancellationToken);
            }
            catch (Exception ex)
            {
                var errorOutput = new QuestionGenerationSSEOutput
                {
                    Success = false,
                    MessageType = "error",
                    Content = ex.Message
                };
                string errorData = "data: " + Newtonsoft.Json.JsonConvert.SerializeObject(errorOutput) + "\n\n";
                await Response.WriteAsync(errorData, cancellationToken);
                await Response.Body.FlushAsync(cancellationToken);
            }
        }

        /// <summary>
        /// 重新生成单题
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<AIGeneratedQuestion> RegenerateQuestion(RegenerateQuestionInput input)
        {
            try
            {
                // 参数验证
                if (input.OriginalQuestion == null)
                {
                    throw new BusException("原题目信息不能为空!", 801);
                }

                if (string.IsNullOrEmpty(input.OriginalQuestion.QuestionType))
                {
                    throw new BusException("原题目类型不能为空!", 801);
                }

                if (string.IsNullOrEmpty(input.OriginalQuestion.Title))
                {
                    throw new BusException("原题目题干不能为空!", 801);
                }

                if (string.IsNullOrEmpty(input.AIModeId))
                {
                    throw new BusException("请选择AI模型!", 801);
                }

                // 调用服务重新生成题目
                var result = await _agentIntelligentQuestionService.RegenerateQuestion(input);

                if (result.Success && result.Question != null)
                {
                    return result.Question;
                }
                else
                {
                    throw new BusException(result.ErrorMessage ?? "重新生成题目失败", 801);
                }
            }
            catch (BusException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw new BusException("系统异常: " + ex.Message, 801);
            }
        }

        /// <summary>
        /// 单题保存到题库
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<SaveToQuestionBankOutput> SaveSingleQuestionToBank(SaveSingleQuestionToBankInput input)
        {
            try
            {
                // 参数验证
                if (input.Question == null)
                {
                    throw new BusException("题目信息不能为空!", 801);
                }

                if (string.IsNullOrEmpty(input.Question.Title))
                {
                    throw new BusException("题目题干不能为空!", 801);
                }

                //if (string.IsNullOrEmpty(input.ChapterId))
                //{
                //    throw new BusException("章节ID不能为空!", 801);
                //}

                input.UserId = UserId;
                // 调用服务保存题目
                var result = await _agentIntelligentQuestionService.SaveSingleQuestionToBank(input);

                if (result.Success)
                {
                    return result;
                }
                else
                {
                    throw new BusException(result.ErrorMessage ?? "题目保存失败", 801);
                }
            }
            catch (BusException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw new BusException("系统异常: " + ex.Message, 801);
            }
        }

        /// <summary>
        /// 批量保存到题库
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<SaveToQuestionBankOutput> SaveBatchQuestionsToBank(SaveBatchQuestionsToBankInput input)
        {
            try
            {
                // 参数验证
                if (input.Questions == null || input.Questions.Count == 0)
                {
                    throw new BusException("题目列表不能为空!", 801);
                }

                //if (string.IsNullOrEmpty(input.ChapterId))
                //{
                //    throw new BusException("章节ID不能为空!", 801);
                //}

                input.UserId = UserId;
                // 调用服务批量保存题目
                var result = await _agentIntelligentQuestionService.SaveBatchQuestionsToBank(input);

                if (result.Success)
                {
                    return result;
                    //return new ApiResult<SaveToQuestionBankOutput>
                    //{
                    //    code = 200,
                    //    message = $"批量保存完成，成功{result.SuccessCount}个，失败{result.FailedCount}个",
                    //    data = result
                    //};
                }
                else
                {
                    //return new ApiResult<SaveToQuestionBankOutput>
                    //{
                    //    code = 500,
                    //    message = result.ErrorMessage ?? "批量保存失败",
                    //    data = result
                    //};
                    throw new BusException(result.ErrorMessage ?? "批量保存失败", 801);
                }
            }
            catch (BusException ex)
            {
                throw ex;
            }
            catch (Exception ex)
            {
                throw new BusException("系统异常: " + ex.Message, 801);
            }
        }
    }
}
