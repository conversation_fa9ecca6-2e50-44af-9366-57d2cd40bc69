﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UwooAgent.Entity.DomainModels.AI
{
    /// <summary>
	/// AI模型配置
	/// </summary>
	[Table("AI_AgentBot")]
    public class AI_ModelConfig
    {
        /// <summary>
		/// Id
		/// </summary>
		[SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 场景类型
        /// </summary>
        public int SceneType { get; set; }

        /// <summary>
        /// 场景名称
        /// </summary>
        public string SceneName { get; set; }

        /// <summary>
        /// 豆包模型key
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }
}
