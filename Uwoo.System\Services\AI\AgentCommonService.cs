﻿using Azure;
using Coldairarrow.Util;
using NetTaste;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.WebSockets;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using ThirdParty.Json.LitJson;
using Uwoo.Core.BaseProvider;
using Uwoo.Core.Configuration;
using Uwoo.Core.Extensions;
using Uwoo.Core.Extensions.AutofacManager;
using Uwoo.Core.Services;
using Uwoo.Core.Utilities;
using Uwoo.Model;
using Uwoo.Model.CustomException;
using Uwoo.Model.Upload;
using UwooAgent.Core.Utilities;
using UwooAgent.Entity.DomainModels.AI;
using UwooAgent.Model;
using UwooAgent.Model.AI;
using UwooAgent.System.IRepositories.AI;
using UwooAgent.System.IServices.AI;

namespace UwooAgent.System.Services.AI
{
    /// <summary>
    /// 智能体_通用功能
    /// </summary>
    public class AgentCommonService : ServiceBase<AI_AgentBaseInfo, IAgentCommonRepository>, IAgentCommonService, IDependency
    {
        #region DI
        private readonly HttpClient _httpClient;

        public AgentCommonService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }
        #endregion

        /// <summary>
        /// AI图片地址转华为云OBS
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<List<ImgInput>> AIUrlReplaceHWYOBS(List<ImgInput> input)
        {
            try
            {
                var uploadFileToObs = new UploadHuaWeiObs();
                foreach (var img in input)
                {
                    if (img.Type == 2)
                    {
                        // 发送 HTTP 请求获取响应
                        HttpResponseMessage response = await _httpClient.GetAsync(img.ImgUrl, HttpCompletionOption.ResponseHeadersRead);

                        // 检查响应是否成功
                        response.EnsureSuccessStatusCode();

                        // 获取文件流
                        Stream fileStream = await response.Content.ReadAsStreamAsync();
                        string imgUrl = uploadFileToObs.Upload(fileStream, "AI.JPEG", "aidialoguefileu");
                        img.ImgUrl = imgUrl;
                        img.Type = 1;
                    }
                }
                return input;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// AI_生成图片
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<string> AIGenerateImage(AIGenerateImageInput input)
        {
            try
            {
                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string model = AppSetting.DouBaoAI.AIGenerateImageModelId;
                string url = AppSetting.DouBaoAI.AIGenerateImageUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(model) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //入参处理
                AIGenerateImageDouBaoInput douBaoInput = new AIGenerateImageDouBaoInput()
                {
                    prompt = input.Prompt + "只生成一张图片。",
                    model = model,
                    size = "1024x1024",
                    guidance_scale = 5,
                    watermark = false,
                    response_format = "url"
                };
                string jsonData = JsonConvert.SerializeObject(douBaoInput);

                //http
                using (var httpClient = new HttpClient())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(120);
                    httpClient.DefaultRequestHeaders.Add("Authorization", apiKey);
                    var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                    HttpResponseMessage response = await httpClient.PostAsync(url, content);
                    if (response.IsSuccessStatusCode)
                    {
                        string resultData = await response.Content.ReadAsStringAsync();

                        //返回结果
                        AIGenerateImageDouBaoOutput result = JsonConvert.DeserializeObject<AIGenerateImageDouBaoOutput>(resultData);
                        return result.data[0].url;
                    }
                    else
                    {
                        throw new BusException("生成失败!");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// AI_生成HTML代码
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<AIGenerateHTMLCodeOutput> AIGenerateHTMLCode(AIGenerateHTMLCodeInput input)
        {
            try
            {
                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string model = AppSetting.DouBaoAI.AIGenerateHTMLCodeModeId;
                string url = AppSetting.DouBaoAI.AIGenerateHTMLCodeUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(model) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //入参处理
                List<AIGenerateHTMLCodeDouBaoMessageInput> messages = new List<AIGenerateHTMLCodeDouBaoMessageInput>()
                {
                    new AIGenerateHTMLCodeDouBaoMessageInput(){  role="system", content="你是一个工具生成助手，请根据用户要求生成一个完整的、可直接运行的HTML5页面代码。要求：1. 包含标准的HTML5文档结构（<!DOCTYPE html>、<html lang='zh-CN'>等）2. 代码需包含<head>标签，且设置正确的字符编码（UTF-8）3. 页面标题需有实际内容4. 包含基本的<body>内容，如标题、段落等5. 代码必须是完整且独立的，能够直接保存为.html文件并在浏览器中正常打开运行6. 可以包含简单的CSS样式和JavaScript交互（可选）请直接提供完整代码，不需要额外解释。"},
                    new AIGenerateHTMLCodeDouBaoMessageInput(){ role="user", content=input.Prompt }
                };
                AIGenerateHTMLCodeDouBaoInput hTMLCodeDouBaoInput = new AIGenerateHTMLCodeDouBaoInput()
                {
                    model = model,
                    messages = messages,
                    stream = true,
                    stream_options = new AIGenerateHTMLCodeDouBaoStreamOptionsInput()
                    {
                        include_usage = true
                    },
                    response_format = new AIGenerateHTMLCodeDouBaoResponseFormatInput()
                    {
                        type = "json_schema",
                        json_schema = new AIGenerateHTMLCodeDouBaoJsonSchemaInput()
                        {

                            name = "CodeGenerator",
                            strict = true,
                            schema = new AIGenerateHTMLCodeDouBaoSchemaInput()
                            {
                                HtmlCode = "HTML代码",
                                Name = "工具名称"
                            }
                        }
                    },
                    thinking = new AIGenerateHTMLCodeDouBaoThinkingInput()
                    {
                        type = "enabled"
                    }
                };
                string jsonData = JsonConvert.SerializeObject(hTMLCodeDouBaoInput);

                string resDataJson = string.Empty;

                //http请求
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            using (var stream = await response.Content.ReadAsStreamAsync())
                            {
                                using (var reader = new StreamReader(stream, Encoding.UTF8))
                                {
                                    string line;
                                    while ((line = await reader.ReadLineAsync()) != "data: [DONE]")
                                    {
                                        // 处理SSE数据行
                                        if (line.StartsWith("data:"))
                                        {
                                            //去除(data: )进行解析数据
                                            var data = line.Substring("data:".Length).Trim();
                                            if (!string.IsNullOrEmpty(data))
                                            {
                                                AIGenerateHTMLCodeDouBaoOutput codeDouBaoOutput = JsonConvert.DeserializeObject<AIGenerateHTMLCodeDouBaoOutput>(data);
                                                if (codeDouBaoOutput != null && codeDouBaoOutput.choices.Count > 0 && codeDouBaoOutput.choices[0].delta != null)
                                                {
                                                    resDataJson += codeDouBaoOutput.choices[0].delta.content;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (!string.IsNullOrEmpty(resDataJson))
                {
                    AIGenerateHTMLCodeOutput codeOutput = JsonConvert.DeserializeObject<AIGenerateHTMLCodeOutput>(resDataJson);
                    codeOutput.CreateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    return codeOutput;
                }
                else
                {
                    throw new BusException("生成失败!");
                }
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// AI_生成题目（流式版本）
        /// </summary>
        /// <param name="input">输入参数</param>
        /// <param name="onQuestionGenerated">单个题目生成完成时的回调</param>
        /// <param name="onProgress">进度更新回调</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<AIGenerateQuestionsOutput> AIGenerateQuestionsStream(
            AIGenerateQuestionsInput input,
            Func<AIGeneratedQuestion, int, Task> onQuestionGenerated = null,
            Func<string, Task> onProgress = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string url = AppSetting.DouBaoAI.AIGenerateQuestionsUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(input.Model) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //构建系统提示词
                string systemPrompt = BuildSystemPrompt(input);

                //入参处理
                List<AIGenerateQuestionsDouBaoMessageInput> messages = new List<AIGenerateQuestionsDouBaoMessageInput>()
                {
                    new AIGenerateQuestionsDouBaoMessageInput() { role = "system", content = systemPrompt },
                    new AIGenerateQuestionsDouBaoMessageInput() { role = "user", content = input.Prompt }
                };

                AIGenerateQuestionsDouBaoInput questionsDouBaoInput = new AIGenerateQuestionsDouBaoInput()
                {
                    model = input.Model,
                    messages = messages,
                    stream = true, // 启用流式返回
                    stream_options = new AIGenerateQuestionsDouBaoStreamOptionsInput()
                    {
                        include_usage = true
                    },
                    response_format = new AIGenerateQuestionsDouBaoResponseFormatInput()
                    {
                        type = "json_schema",
                        json_schema = new AIGenerateQuestionsDouBaoJsonSchemaInput()
                        {
                            name = "QuestionGenerator",
                            strict = true,
                            schema = new AIGenerateQuestionsDouBaoSchemaInput()
                        }
                    }
                };

                string jsonData = JsonConvert.SerializeObject(questionsDouBaoInput);

                var allQuestions = new List<AIGeneratedQuestion>();
                var parser = new QuestionStreamParser();

                //http请求
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            using (var stream = await response.Content.ReadAsStreamAsync())
                            {
                                using (var reader = new StreamReader(stream, Encoding.UTF8))
                                {
                                    string line;
                                    while ((line = await reader.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
                                    {
                                        if (line.StartsWith("data: [DONE]"))
                                        {
                                            break;
                                        }

                                        if (line.StartsWith("data:"))
                                        {
                                            var data = line.Substring("data:".Length).Trim();
                                            if (!string.IsNullOrEmpty(data))
                                            {
                                                try
                                                {
                                                    var streamOutput = JsonConvert.DeserializeObject<AIGenerateQuestionsStreamOutput>(data);
                                                    // 安全访问choices数组，避免索引越界
                                                    var content = streamOutput?.choices?.FirstOrDefault()?.delta?.content;

                                                    if (!string.IsNullOrEmpty(content))
                                                    {
                                                        // 处理流式内容，尝试解析完整题目
                                                        var completedQuestion = await ProcessStreamContent(content, parser, onProgress);

                                                        if (completedQuestion != null)
                                                        {
                                                            allQuestions.Add(completedQuestion);

                                                            // 调用回调函数
                                                            if (onQuestionGenerated != null)
                                                            {
                                                                await onQuestionGenerated(completedQuestion, allQuestions.Count);
                                                            }

                                                            // 重置题目状态
                                                            parser.ResetQuestion();
                                                        }
                                                    }
                                                }
                                                catch (Newtonsoft.Json.JsonException ex)
                                                {
                                                    Console.WriteLine($"解析流式数据失败: {ex.Message}, 数据: {data}");
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            string errorContent = await response.Content.ReadAsStringAsync();
                            throw new Exception($"HTTP请求失败: {response.StatusCode}, 内容: {errorContent}");
                        }
                    }
                }

                return new AIGenerateQuestionsOutput
                {
                    Questions = allQuestions,
                    CreateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// AI_生成题目（原版本，保持兼容性）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<AIGenerateQuestionsOutput> AIGenerateQuestions(AIGenerateQuestionsInput input)
        {
            try
            {
                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string url = AppSetting.DouBaoAI.AIGenerateQuestionsUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(input.Model) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //构建系统提示词
                // 根据出题模式构建不同的系统提示词
                string systemPrompt;
                if (input.IsKnowledgePointMode && input.KnowledgePointMapping != null && input.KnowledgePointMapping.Count > 0)
                {
                    // 构建知识点映射信息
                    var knowledgePointInfo = string.Join("、", input.KnowledgePointMapping.Select(kv => $"{kv.Value}(ID:{kv.Key})"));

                    systemPrompt = $@"你是一个专业的出题助手，请根据用户要求生成题目。

当前是知识点出题模式，可选的知识点包括：{knowledgePointInfo}

要求：
1. 严格按照指定的JSON格式返回数据
2. 题型包括：单项选择题（2）、多项选择题（10）、判断题（11）、填空题（5）、主观题（36）
3. 选择题和判断题必须包含选项，填空题和主观题不需要选项
4. 单项选择题选项用A、B、C、D标识
5. 多项选择题选项用A、B、C、D标识，答案用字母组合表示，字母之间用竖线符号分割（如：A|C）
6. 判断题选项用A、B标识，A表示正确，B表示错误
7. 填空题使用中文括号格式，在题干中用（答案）表示填空位置（括号内为答案），答案字段填写正确答案，多个空格的答案用竖线|分隔
8. 主观题提供参考答案要点
9. 每道题必须包含详细的答案解析
10. **重要：每道题必须在KnowledgePoints数组中列出该题实际涉及的具体知识点，包含正确的ID和Content**

返回格式示例：
{{
  ""questions"": [
    {{
      ""QuestionType"": ""单项选择题"",
      ""QuestionTypeId"":2,
      ""Title"": ""这里是题干内容"",
      ""Options"": [
        {{""Option"": ""A"", ""Content"": ""选项A内容""}},
        {{""Option"": ""B"", ""Content"": ""选项B内容""}},
        {{""Option"": ""C"", ""Content"": ""选项C内容""}},
        {{""Option"": ""D"", ""Content"": ""选项D内容""}}
      ],
      ""Answer"": ""A"",
      ""Analysis"": ""这是答案解析"",
      ""KnowledgePoints"": [
        {{""Id"": ""kp001"", ""Content"": ""整数运算""}},
        {{""Id"": ""kp002"", ""Content"": ""数与代数""}}
      ]
    }},
    {{
      ""QuestionType"": ""填空题"",
        ""QuestionTypeId"":5,
      ""Title"": ""根据 9 + 6 = 15，可以写出减法算式：（15）-（9）=（6）"",
      ""Answer"": ""15|9|6"",
      ""Analysis"": ""根据加法和减法的关系，15-9=6，15-6=9"",
      ""KnowledgePoints"": [
        {{""Id"": ""kp001"", ""Content"": ""整数运算""}}
      ]
    }}
  ]
}}";
                }
                else if (input.IsChapterMode && input.ChapterMapping != null && input.ChapterMapping.Count > 0)
                {
                    // 构建章节映射信息
                    var chapterInfo = string.Join("、", input.ChapterMapping.Select(kv => $"{kv.Value}(ID:{kv.Key})"));

                    systemPrompt = $@"你是一个专业的出题助手，请根据用户要求生成题目。

当前是章节出题模式，可选的章节包括：{chapterInfo}

要求：
1. 严格按照指定的JSON格式返回数据
2. 题型包括：单项选择题（2）、多项选择题（10）、判断题（11）、填空题（5）、主观题（36）
3. 选择题和判断题必须包含选项，填空题和主观题不需要选项
4. 单项选择题选项用A、B、C、D标识
5. 多项选择题选项用A、B、C、D标识，答案用字母组合表示，字母之间用竖线符号分割（如：A|C）
6. 判断题选项用A、B标识，A表示正确，B表示错误
7. 填空题使用中文括号格式，在题干中用（　）表示填空位置（括号内为空），答案字段填写正确答案，多个空格的答案用竖线|分隔
8. 主观题提供参考答案要点
9. 每道题必须包含详细的答案解析
10. **重要：每道题必须在Chapters数组中列出该题实际涉及的具体章节，包含正确的ID和ChapterName**

返回格式示例：
{{
  ""questions"": [
    {{
      ""QuestionType"": ""单项选择题"",
      ""QuestionTypeId"":2,
      ""Title"": ""这里是题干内容"",
      ""Options"": [
        {{""Option"": ""A"", ""Content"": ""选项A内容""}},
        {{""Option"": ""B"", ""Content"": ""选项B内容""}},
        {{""Option"": ""C"", ""Content"": ""选项C内容""}},
        {{""Option"": ""D"", ""Content"": ""选项D内容""}}
      ],
      ""Answer"": ""A"",
      ""Analysis"": ""这是答案解析"",
      ""Chapters"": [
        {{""Id"": ""ch001"", ""ChapterName"": ""第一章 基础知识""}}
      ]
    }},
    {{
      ""QuestionType"": ""填空题"",
      ""QuestionTypeId"":5,
      ""Title"": ""根据 9 + 6 = 15，可以写出减法算式：（　）-（　）=（　）"",
      ""Answer"": ""15|9|6"",
      ""Analysis"": ""根据加法和减法的关系，15-9=6，15-6=9"",
      ""Chapters"": [
        {{""Id"": ""ch001"", ""ChapterName"": ""第一章 基础知识""}}
      ]
    }}
  ]
}}";
                }
                else
                {
                    systemPrompt = @"你是一个专业的出题助手，请根据用户要求生成题目。

要求：
1. 严格按照指定的JSON格式返回数据
2. 题型包括：单项选择题（2）、多项选择题（10）、判断题（11）、填空题（5）、主观题（36）
3. 选择题和判断题必须包含选项，填空题和主观题不需要选项
4. 单项选择题选项用A、B、C、D标识
5. 多项选择题选项用A、B、C、D标识，答案用字母组合表示，字母之间用竖线符号分割（如：A|C）
6. 判断题选项用A、B标识，A表示正确，B表示错误
7. 填空题使用中文括号格式，在题干中用（　）表示填空位置（括号内为空），答案字段填写正确答案，多个空格的答案用竖线|分隔
8. 主观题提供参考答案要点
9. 每道题必须包含详细的答案解析

返回格式示例：
{
  ""questions"": [
    {
      ""QuestionType"": ""单项选择题"",
      ""QuestionTypeId"":2,
      ""Title"": ""这里是题干内容"",
      ""Options"": [
        {""Option"": ""A"", ""Content"": ""选项A内容""},
        {""Option"": ""B"", ""Content"": ""选项B内容""},
        {""Option"": ""C"", ""Content"": ""选项C内容""},
        {""Option"": ""D"", ""Content"": ""选项D内容""}
      ],
      ""Answer"": ""A"",
      ""Analysis"": ""这是答案解析""
    },
    {
      ""QuestionType"": ""填空题"",
      ""QuestionTypeId"":5,
      ""Title"": ""根据 9 + 6 = 15，可以写出减法算式：（　）-（　）=（　）"",
      ""Answer"": ""15|9|6"",
      ""Analysis"": ""根据加法和减法的关系，15-9=6，15-6=9""
    }
  ]
}";
                }

                //入参处理
                List<AIGenerateQuestionsDouBaoMessageInput> messages = new List<AIGenerateQuestionsDouBaoMessageInput>()
                {
                    new AIGenerateQuestionsDouBaoMessageInput() { role = "system", content = systemPrompt },
                    new AIGenerateQuestionsDouBaoMessageInput() { role = "user", content = input.Prompt }
                };

                AIGenerateQuestionsDouBaoInput questionsDouBaoInput = new AIGenerateQuestionsDouBaoInput()
                {
                    model = input.Model,
                    messages = messages,
                    stream = false,
                    response_format = new AIGenerateQuestionsDouBaoResponseFormatInput()
                    {
                        type = "json_schema",
                        json_schema = new AIGenerateQuestionsDouBaoJsonSchemaInput()
                        {
                            name = "QuestionGenerator",
                            strict = true,
                            schema = new AIGenerateQuestionsDouBaoSchemaInput()
                        }
                    }
                };

                string jsonData = JsonConvert.SerializeObject(questionsDouBaoInput);

                //http请求
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            string responseContent = await response.Content.ReadAsStringAsync();
                            AIGenerateQuestionsDouBaoOutput questionsDouBaoOutput = JsonConvert.DeserializeObject<AIGenerateQuestionsDouBaoOutput>(responseContent);

                            if (questionsDouBaoOutput != null && questionsDouBaoOutput.choices.Count > 0 && !string.IsNullOrEmpty(questionsDouBaoOutput.choices[0].message.content))
                            {
                                // 获取AI返回的JSON字符串
                                string jsonContent = questionsDouBaoOutput.choices[0].message.content;

                                // 添加调试日志
                                Console.WriteLine($"AI返回的原始JSON内容: {jsonContent}");

                                try
                                {
                                    // 清理JSON字符串，移除可能的BOM和多余空白字符
                                    jsonContent = jsonContent.Trim().TrimStart('\uFEFF');

                                    // 移除可能的Markdown代码块标记
                                    if (jsonContent.StartsWith("```json"))
                                    {
                                        jsonContent = jsonContent.Substring(7); // 移除 "```json"
                                    }
                                    if (jsonContent.StartsWith("```"))
                                    {
                                        jsonContent = jsonContent.Substring(3); // 移除 "```"
                                    }
                                    if (jsonContent.EndsWith("```"))
                                    {
                                        jsonContent = jsonContent.Substring(0, jsonContent.Length - 3); // 移除结尾的 "```"
                                    }

                                    // 再次清理空白字符
                                    jsonContent = jsonContent.Trim();

                                    // 方案1：尝试直接反序列化为AIQuestionsWrapper
                                    try
                                    {
                                        var aiResponse = JsonConvert.DeserializeObject<AIQuestionsWrapper>(jsonContent);

                                        AIGenerateQuestionsOutput output = new AIGenerateQuestionsOutput();
                                        output.CreateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                                        if (aiResponse?.questions != null && aiResponse.questions.Count > 0)
                                        {
                                            output.Questions = aiResponse.questions;
                                        }

                                        Console.WriteLine($"成功解析，获得{output.Questions.Count}道题目");
                                        return output;
                                    }
                                    catch (Exception ex1)
                                    {
                                        Console.WriteLine($"方案1失败: {ex1.Message}");

                                        // 方案2：使用JObject进行安全解析
                                        try
                                        {
                                            var jsonObject = Newtonsoft.Json.Linq.JObject.Parse(jsonContent);
                                            var questionsArray = jsonObject["questions"] as Newtonsoft.Json.Linq.JArray;

                                            AIGenerateQuestionsOutput output = new AIGenerateQuestionsOutput();
                                            output.CreateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                                            if (questionsArray != null)
                                            {
                                                foreach (var questionToken in questionsArray)
                                                {
                                                    var question = new AIGeneratedQuestion
                                                    {
                                                        QuestionType = questionToken["QuestionType"]?.ToString(),
                                                        Title = questionToken["Title"]?.ToString(),
                                                        QuestionTypeId = questionToken["QuestionTypeId"]?.ToString(),
                                                        Answer = questionToken["Answer"]?.ToString(),
                                                        Analysis = questionToken["Analysis"]?.ToString()
                                                    };

                                                    // 处理选项
                                                    var optionsArray = questionToken["Options"] as Newtonsoft.Json.Linq.JArray;
                                                    if (optionsArray != null)
                                                    {
                                                        question.Options = new List<AIQuestionOption>();
                                                        foreach (var optionToken in optionsArray)
                                                        {
                                                            question.Options.Add(new AIQuestionOption
                                                            {
                                                                Option = optionToken["Option"]?.ToString(),
                                                                Content = optionToken["Content"]?.ToString()
                                                            });
                                                        }
                                                    }

                                                    // 处理知识点
                                                    var knowledgePointsArray = questionToken["KnowledgePoints"] as Newtonsoft.Json.Linq.JArray;
                                                    if (knowledgePointsArray != null)
                                                    {
                                                        question.KnowledgePoints = new List<QuestionKnowledgePoint>();
                                                        foreach (var kpToken in knowledgePointsArray)
                                                        {
                                                            question.KnowledgePoints.Add(new QuestionKnowledgePoint
                                                            {
                                                                Id = kpToken["Id"]?.ToString() ?? "",
                                                                Content = kpToken["Content"]?.ToString() ?? "",
                                                                Level = kpToken["Level"]?.ToObject<int>() ?? 0
                                                            });
                                                        }
                                                    }

                                                    // 处理章节
                                                    var chaptersArray = questionToken["Chapters"] as Newtonsoft.Json.Linq.JArray;
                                                    if (chaptersArray != null)
                                                    {
                                                        question.Chapters = new List<QuestionChapter>();
                                                        foreach (var chapterToken in chaptersArray)
                                                        {
                                                            question.Chapters.Add(new QuestionChapter
                                                            {
                                                                Id = chapterToken["Id"]?.ToString() ?? "",
                                                                ChapterName = chapterToken["ChapterName"]?.ToString() ?? ""
                                                            });
                                                        }
                                                    }

                                                    output.Questions.Add(question);
                                                }
                                            }

                                            Console.WriteLine($"方案2成功解析，获得{output.Questions.Count}道题目");
                                            return output;
                                        }
                                        catch (Exception ex2)
                                        {
                                            Console.WriteLine($"方案2也失败: {ex2.Message}");
                                            throw new BusException($"AI返回的JSON解析失败。原始内容: {jsonContent.Substring(0, Math.Min(500, jsonContent.Length))}...");
                                        }
                                    }
                                }
                                catch (Exception jsonEx)
                                {
                                    Console.WriteLine($"JSON处理异常: {jsonEx.Message}");
                                    throw new BusException($"AI返回的JSON处理异常: {jsonEx.Message}。返回内容前500字符: {jsonContent.Substring(0, Math.Min(500, jsonContent.Length))}");
                                }
                            }
                            else
                            {
                                throw new BusException("AI生成题目失败，返回内容为空!");
                            }
                        }
                        else
                        {
                            string errorContent = await response.Content.ReadAsStringAsync();
                            throw new BusException($"AI生成题目失败: {response.StatusCode} - {errorContent}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BusException($"AI生成题目异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取AI文件消息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<List<AIFileInfoDto>> GetAIFileInfoList()
        {
            try
            {
                //获取文件信息
                List<AIFileInfoDto> aIFileInfoDtos = await DBSqlSugar.Queryable<AI_FileInfo>()
                    .Where(p => p.Type == 1 && p.IsDeleted == false).Select(p => new AIFileInfoDto()
                    {
                        Name = p.FileName,
                        Url = p.FileUrl,
                        CreateTime = p.CreateTime
                    }).ToListAsync();

                return aIFileInfoDtos;
            }
            catch (Exception ex)
            {
                throw new BusException(ex.Message);
            }
        }

        /// <summary>
        /// 创建上下文缓存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<string> CreateContext(CreateContextInput input)
        {
            try
            {
                //消息不能为空，过期时间可以设置的范围在1小时到7天，即[3600, 604800]单位：秒
                if (string.IsNullOrEmpty(input.Msg) || string.IsNullOrEmpty(input.modelId) || (input.TimeOut < 3600 || input.TimeOut > 604800))
                {
                    return null;
                }

                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string url = AppSetting.DouBaoAI.CreateContextUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //创建上下文入参
                CreateContextDouBaoInput createContextInputDto = new CreateContextDouBaoInput()
                {
                    model = input.modelId,
                    mode = "session",
                    ttl = input.TimeOut,
                    messages = new List<CreateContextDouBaoMessage>()
                    {
                        new CreateContextDouBaoMessage()
                        {
                            role="system",
                            content=input.Msg
                        }
                    },
                    truncation_strategy = new CreateContextDouBaoTruncationStrategy()
                    {
                        type = "rolling_tokens",
                        rolling_tokens = true
                    }
                };
                string jsonStr = JsonConvert.SerializeObject(createContextInputDto);

                //http请求
                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", apiKey);
                    var content = new StringContent(jsonStr, Encoding.UTF8, "application/json");
                    HttpResponseMessage response = await httpClient.PostAsync(url, content);
                    if (response.IsSuccessStatusCode)
                    {
                        string resultData = await response.Content.ReadAsStringAsync();

                        //返回结果
                        JObject result = (JObject)JsonConvert.DeserializeObject(resultData);
                        return result["id"].ToString();
                    }
                    else
                    {
                        return "";
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 上下文对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task ContextDialogue(ContextDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken = default)
        {
            try
            {
                if (string.IsNullOrEmpty(input.role) || string.IsNullOrEmpty(input.content) || string.IsNullOrEmpty(input.context_id) || string.IsNullOrEmpty(input.modelId))
                {
                    throw new Exception("参数异常!");
                }

                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string url = AppSetting.DouBaoAI.ContextDialogueUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //参数处理
                ContextDialogueDouBaoInput douBaoContextDialogueInputDto = new ContextDialogueDouBaoInput()
                {
                    context_id = input.context_id,
                    model = input.modelId,
                    stream = true,
                    stream_options = new ContextDialogueDouBaoInputStreamOptions()
                    {
                        include_usage = true
                    },
                    messages = new List<ContextDialogueDouBaoInputMessages>()
                     {
                         new ContextDialogueDouBaoInputMessages()
                         {
                             role=input.role,
                             content=input.content
                         }
                     }
                };
                string jsonInputDto = JsonConvert.SerializeObject(douBaoContextDialogueInputDto);

                //http请求
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonInputDto, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            using (var stream = await response.Content.ReadAsStreamAsync())
                            {
                                using (var reader = new StreamReader(stream, Encoding.UTF8))
                                {
                                    string line;
                                    while ((line = await reader.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
                                    {
                                        if (line.StartsWith("data: [DONE]"))
                                        {
                                            break;
                                        }
                                        // 处理SSE数据行
                                        if (line.StartsWith("data:"))
                                        {
                                            //去除(data: )进行解析数据
                                            var data = line.Substring("data:".Length).Trim();
                                            if (!string.IsNullOrEmpty(data))
                                            {
                                                //解析
                                                ContextDialogueDouBaoOutput douBaoOutputDto = JsonConvert.DeserializeObject<ContextDialogueDouBaoOutput>(data);
                                                if (douBaoOutputDto.choices.Count > 0 && douBaoOutputDto.choices[0].delta != null)
                                                {
                                                    AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                                                    {
                                                        Success = true,
                                                        Content = douBaoOutputDto.choices[0].delta.content
                                                    };
                                                    //SSE推送客户端
                                                    string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                                                    await dataHandler(ssePushData);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            string errorMsg = await response.Content.ReadAsStringAsync();
                            AgentSSEOutput errorSSEOutput = new AgentSSEOutput()
                            {
                                Success = false,
                                Content = $"第三方接口异常：{response.StatusCode}，详情：{errorMsg}"
                            };
                            //SSE推送客户端
                            string errorPushData = "data: " + JsonConvert.SerializeObject(errorSSEOutput) + "\n\n";
                            await dataHandler(errorPushData);
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// AI对话
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        public async Task AIDialogue(AIDialogueInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken = default)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Msg) || string.IsNullOrEmpty(input.ModelKey))
                {
                    throw new Exception("参数异常!");
                }

                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string url = AppSetting.DouBaoAI.DialogueUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //入参处理
                List<AIDialogueDouBaoMessageInput> messages = new List<AIDialogueDouBaoMessageInput>()
                {
                    new AIDialogueDouBaoMessageInput(){ role="user", content=input.Msg }
                };
                AIDialogueDouBaoInput dialogueDouBaoInput = new AIDialogueDouBaoInput()
                {
                    model = input.ModelKey,
                    messages = messages,
                    stream = true,
                    stream_options = new AIDialogueDouBaoStreamOptionsInput()
                    {
                        include_usage = true
                    },
                    thinking = new AIDialogueDouBaoThinkingInput()
                    {
                        type = "enabled"
                    }
                };
                string jsonData = JsonConvert.SerializeObject(dialogueDouBaoInput);

                //http请求
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            using (var stream = await response.Content.ReadAsStreamAsync())
                            {
                                using (var reader = new StreamReader(stream, Encoding.UTF8))
                                {
                                    string line;
                                    while ((line = await reader.ReadLineAsync()) != null && !cancellationToken.IsCancellationRequested)
                                    {
                                        if (line.StartsWith("data: [DONE]"))
                                        {
                                            break;
                                        }
                                        // 处理SSE数据行
                                        if (line.StartsWith("data:"))
                                        {
                                            //去除(data: )进行解析数据
                                            var data = line.Substring("data:".Length).Trim();
                                            if (!string.IsNullOrEmpty(data))
                                            {
                                                AIDialogueDouBaoOutput douBaoOutput = JsonConvert.DeserializeObject<AIDialogueDouBaoOutput>(data);
                                                if (douBaoOutput != null && douBaoOutput.choices.Count > 0 && douBaoOutput.choices[0].delta != null)
                                                {
                                                    AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                                                    {
                                                        Success = true,
                                                        Content = douBaoOutput.choices[0].delta.content
                                                    };
                                                    //SSE推送客户端
                                                    string ssePushData = "data: " + JsonConvert.SerializeObject(agentSSEOutput) + "\n\n";
                                                    await dataHandler(ssePushData);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            string errorMsg = await response.Content.ReadAsStringAsync();
                            AgentSSEOutput errorSSEOutput = new AgentSSEOutput()
                            {
                                Success = false,
                                Content = $"第三方接口异常：{response.StatusCode}，详情：{errorMsg}"
                            };
                            //SSE推送客户端
                            string errorPushData = "data: " + JsonConvert.SerializeObject(errorSSEOutput) + "\n\n";
                            await dataHandler(errorPushData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 音频文件转文本
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task<string> AudioFileChangeText(AudioFileChangeTextInput input)
        {
            try
            {
                string fileExtension = Path.GetExtension(input.FileUrl);
                if (fileExtension.ToLower() != ".wav")
                {
                    throw new Exception("非常抱歉，目前我们仅支持 wav 这一种音频格式，还请您谅解!");
                }

                //获取配置
                string appId = AppSetting.DouBaoAI.VoiceAppId;
                string token = AppSetting.DouBaoAI.VoiceToken;
                string url = AppSetting.DouBaoAI.AudioFileChangeTextUrl;
                if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(token) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //参数处理
                AudioFileChangeTextDouBaoInput douBao_SubmitAudioFileChangeTextInputDto = new AudioFileChangeTextDouBaoInput()
                {
                    user = new AudioFileChangeTextDouBaoInputUser()
                    {
                        uid = Guid.NewGuid().ToString()
                    },
                    audio = new AudioFileChangeTextDouBaoInputAudio()
                    {
                        url = input.FileUrl
                    },
                    request = new AudioFileChangeTextDouBaoInputRequest()
                    {
                        model_name = "bigmodel"
                    }
                };
                string jsonData = JsonConvert.SerializeObject(douBao_SubmitAudioFileChangeTextInputDto);

                //http请求
                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("X-Api-App-Key", appId);
                    httpClient.DefaultRequestHeaders.Add("X-Api-Access-Key", token);
                    httpClient.DefaultRequestHeaders.Add("X-Api-Resource-Id", "volc.bigasr.auc_turbo");
                    httpClient.DefaultRequestHeaders.Add("X-Api-Request-Id", Guid.NewGuid().ToString());
                    httpClient.DefaultRequestHeaders.Add("X-Api-Sequence", "-1");
                    var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                    HttpResponseMessage response = await httpClient.PostAsync(url, content);

                    string code = response.Headers.TryGetValues("X-Api-Status-Code", out var values) ? values.FirstOrDefault() : "";
                    if (response.IsSuccessStatusCode && !string.IsNullOrEmpty(code) && code == "20000000")
                    {
                        string responseContent = await response.Content.ReadAsStringAsync();

                        //返回结果
                        JObject resultData = (JObject)JsonConvert.DeserializeObject(responseContent);
                        JObject resultText = (JObject)JsonConvert.DeserializeObject(resultData["result"].ToString());

                        return resultText["text"].ToString();
                    }
                    else
                    {
                        throw new BusException($"无法识别语音内容", 801);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new BusException($"语音转文本失败:{ex.Message}，语音文件地址:{input.FileUrl}");
            }
        }

        /// <summary>
        /// 文本转语音
        /// </summary>
        /// <param name="input"></param>
        /// <param name="dataHandler"></param>
        /// <returns></returns>
        /// <exception cref="BusException"></exception>
        public async Task TextChangeVoice(AITextChangeVoiceInput input, Func<string, Task> dataHandler, CancellationToken cancellationToken)
        {
            try
            {
                //公式处理
                int i = 0;
                string pattern = @"\\\((.*?)\\\)";
                List<string> equations = new List<string>();
                MatchCollection blanks = Regex.Matches(input.Text, pattern);
                foreach (Match match in blanks)
                {
                    equations.Add(match.Value);
                    input.Text = input.Text.Replace(match.Value, $"YWEquation{i}YWEquation");
                    i++;
                }

                input.Text = input.Text.Replace("|", ",").Replace(" ", "");

                //学生姓名Id处理/图片处理
                string patternStu = @"\[.*?\]\([^)]+\)";
                MatchCollection blanksStu = Regex.Matches(input.Text, patternStu);
                foreach (Match match in blanksStu)
                {
                    string matchText = match.Value;
                    matchText = Regex.Replace(matchText, @"\([^)]*\)", "").Replace("[", "").Replace("]", "");
                    input.Text = input.Text.Replace(match.Value, matchText);
                }

                //还原公式
                foreach (var equation in equations)
                {
                    input.Text = input.Text.Replace($"YWEquation{equations.IndexOf(equation)}YWEquation", equation);
                }

                //获取配置
                string appId = AppSetting.DouBaoAI.VoiceAppId;
                string token = AppSetting.DouBaoAI.VoiceToken;
                string url = AppSetting.DouBaoAI.TextChangeVoiceUrl;
                //替换音色代号
                string speaker = "zh_female_tianmeixiaoyuan_moon_bigtts";
                if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(token) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //webSocket
                using (var webSocket = new ClientWebSocket())
                {
                    try
                    {
                        //webSocket请求头
                        webSocket.Options.SetRequestHeader("X-Api-App-Key", appId);
                        webSocket.Options.SetRequestHeader("X-Api-Access-Key", token);
                        webSocket.Options.SetRequestHeader("X-Api-Resource-Id", "volc.service_type.10029");
                        webSocket.Options.SetRequestHeader("X-Api-Connect-Id", Guid.NewGuid().ToString());

                        //建立webSocket连接
                        await webSocket.ConnectAsync(new Uri(url), CancellationToken.None);

                        var hasFinishedSession = false;
                        var sessionId = Guid.NewGuid().ToString().Replace("-", "");

                        //开始连接
                        await DouBaoTTSWebSocketBusiness.StartConnection(webSocket);

                        var buffer = new byte[1024 * 1024 * 100];
                        while (webSocket.State == WebSocketState.Open)
                        {
                            //响应
                            var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                            if (result.MessageType == WebSocketMessageType.Close)
                            {
                                //webSocket已关闭
                                break;
                            }
                            else
                            {
                                var responseBytes = new byte[result.Count];
                                Array.Copy(buffer, responseBytes, result.Count);
                                //解析响应
                                var response = DouBaoTTSWebSocketBusiness.ParserResponse(responseBytes);
                                switch (response.optional.eventType)
                                {
                                    case DouBaoTTSWebSocketBusiness.EVENT_ConnectionFailed:
                                        throw new Exception("建联失败:" + response.optional.eventType);
                                    case DouBaoTTSWebSocketBusiness.EVENT_SessionFailed:
                                        throw new Exception("会话失败:" + response.optional.eventType);
                                    case DouBaoTTSWebSocketBusiness.EVENT_ConnectionStarted:

                                        //开始会话
                                        await DouBaoTTSWebSocketBusiness.StartTTSSession(webSocket, sessionId, speaker);
                                        break;

                                    case DouBaoTTSWebSocketBusiness.EVENT_SessionStarted:

                                        //发送信息
                                        await DouBaoTTSWebSocketBusiness.SendTTSMessage(webSocket, speaker, sessionId, input.Text);
                                        break;

                                    case DouBaoTTSWebSocketBusiness.EVENT_TTSSentenceStart:

                                        break;

                                    case DouBaoTTSWebSocketBusiness.EVENT_TTSResponse:

                                        if (response.payload == null)
                                        {
                                            continue;
                                        }
                                        if (response.header.message_type == DouBaoTTSWebSocketBusiness.AUDIO_ONLY_RESPONSE)
                                        {
                                            if (cancellationToken.IsCancellationRequested)
                                            {
                                                return;
                                            }
                                            string base64String = Convert.ToBase64String(response.payload);

                                            AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                                            {
                                                Success = true,
                                                Content = base64String
                                            };
                                            string ssePushData = "data: " + agentSSEOutput.ToJsonString() + "\n\n";
                                            await dataHandler(ssePushData);
                                        }
                                        break;
                                    case DouBaoTTSWebSocketBusiness.EVENT_TTSSentenceEnd:

                                        if (!hasFinishedSession)
                                        {
                                            hasFinishedSession = await DouBaoTTSWebSocketBusiness.FinishSession(webSocket, sessionId);
                                        }
                                        break;

                                    case DouBaoTTSWebSocketBusiness.EVENT_SessionFinished:

                                        await DouBaoTTSWebSocketBusiness.FinishConnection(webSocket);
                                        break;

                                    case DouBaoTTSWebSocketBusiness.EVENT_ConnectionFinished:

                                        await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                                        break;

                                    default:
                                        break;
                                }
                            }
                        }

                        //结束
                        AgentSSEOutput agentSSEDoneOutput = new AgentSSEOutput()
                        {
                            Success = true,
                            Content = "DONE"
                        };
                        string ssePushDoneData = "data: " + agentSSEDoneOutput.ToJsonString() + "\n\n";
                        await dataHandler(ssePushDoneData);
                    }
                    catch (Exception ex)
                    {
                        AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                        {
                            Success = false,
                            Content = ex.Message
                        };
                        string ssePushData = "data: " + agentSSEOutput.ToJsonString() + "\n\n";
                        await dataHandler(ssePushData);
                    }
                    finally
                    {
                        if (webSocket.State == WebSocketState.Open)
                        {
                            await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
                        }
                        webSocket.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                AgentSSEOutput agentSSEOutput = new AgentSSEOutput()
                {
                    Success = false,
                    Content = ex.Message
                };
                string ssePushData = "data: " + agentSSEOutput.ToJsonString() + "\n\n";
                await dataHandler(ssePushData);
            }
        }

        /// <summary>
        /// 获取AI对话内容记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageReturn<GetAIDialogueContentRecordOutput>> GetAIDialogueContentRecord(GetAIDialogueContentRecordInput input)
        {
            try
            {
                //获取相关key
                string key = string.Empty;
                if (input.Type == 0)
                {
                    //获取学生端-口语交际key
                    key = AIAgentKeys.GetStudentOralCommunicationKey(input.AgentId, input.AgentTaskId, input.StudentId);
                }
                if (string.IsNullOrEmpty(key))
                {
                    throw new Exception("无法获取相关Key!");
                }

                //获取数据
                string sql = @"SELECT
                                	dcr.Id,
                                	dcr.Ask,
                                	dcr.Answer,
                                	dcr.MessageType,
                                	dcr.CreateTime
                                FROM
                                	AI_DialogueContentRecord dcr WITH ( NOLOCK )
                                WHERE
                                	dcr.[Key] =@key
                                    AND dcr.IsDeleted=0";
                RefAsync<int> totalNumber = 0;
                List<GetAIDialogueContentRecordOutput> datas = await DBSqlSugar.SqlQueryable<GetAIDialogueContentRecordOutput>(sql)
                    .AddParameters(new { key = key })
                    .OrderBy("CreateTime desc")
                    .ToPageListAsync(input.PageIndex, input.PageSize, totalNumber);

                foreach (var data in datas)
                {
                    data.AskInfo = JsonConvert.DeserializeObject<AIDialogueASKDto>(data.Ask);
                }

                return new PageReturn<GetAIDialogueContentRecordOutput>()
                {
                    Datas = datas.OrderBy(p => p.CreateTime).ToList(),
                    TotalCount = totalNumber
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取智能体模型列表
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<List<GetAgentModelInfoOutput>> GetAgentModelInfo()
        {
            try
            {
                List<GetAgentModelInfoOutput> getAgentModels = await DBSqlSugar.Queryable<AI_ModelBaseInfo>()
                    .Where(p => p.IsDeleted == false)
                    .OrderBy(p => p.OrderId)
                    .Select(p => new GetAgentModelInfoOutput()
                    {
                        Id = p.Id,
                        ModelName = p.ModelName,
                        ModelDescribe = p.ModelDescribe,
                        IsContext = p.IsContext
                    }).ToListAsync();
                return getAgentModels;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 文件分析
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<FileAnalysisDouBaoOutput>> FileAnalysis(FileAnalysisInput input)
        {
            try
            {
                if (input.Msg.Count <= 0)
                {
                    throw new Exception("参数异常!");
                }

                //获取配置
                string apiKey = AppSetting.DouBaoAI.APIKey;
                string model = AppSetting.DouBaoAI.FileAnalysisAppId;
                string url = AppSetting.DouBaoAI.MyAppUrl;
                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(model) || string.IsNullOrEmpty(url))
                {
                    throw new Exception("请求配置异常!");
                }

                //参数处理
                List<DouBaoMyAppMessages> messages = new List<DouBaoMyAppMessages>();
                foreach (var item in input.Msg)
                {
                    messages.Add(new DouBaoMyAppMessages()
                    {
                        role = "user",
                        content = item
                    });
                }
                DouBaoMyAppDto fileAnalysisDouBaoDto = new DouBaoMyAppDto()
                {
                    model = model,
                    stream = true,
                    messages = messages
                };
                string jsonData = JsonConvert.SerializeObject(fileAnalysisDouBaoDto);

                string resDataJson = string.Empty;
                //http请求
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, url);
                    request.Headers.Add("Authorization", apiKey);
                    request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");

                    using (var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            using (var stream = await response.Content.ReadAsStreamAsync())
                            {
                                using (var reader = new StreamReader(stream, Encoding.UTF8))
                                {
                                    string line;
                                    while ((line = await reader.ReadLineAsync()) != "data:[DONE]")
                                    {
                                        // 处理SSE数据行
                                        if (line.StartsWith("data:"))
                                        {
                                            //去除(data: )进行解析数据
                                            var data = line.Substring("data:".Length).Trim();
                                            if (!string.IsNullOrEmpty(data))
                                            {
                                                //解析请求结果
                                                DouBaoMyAppStreamOutput chatResponse = JsonConvert.DeserializeObject<DouBaoMyAppStreamOutput>(data);
                                                if (chatResponse != null && chatResponse.choices.Count > 0 && chatResponse.choices[0].delta != null)
                                                {
                                                    resDataJson += chatResponse.choices[0].delta.content;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                string jsonDataText = string.Empty;
                //使用正则表达式匹配Json
                string jsonPattern = @"\[.*?\]";
                Match match = Regex.Match(resDataJson, jsonPattern, RegexOptions.Singleline);
                if (match.Success)
                {
                    jsonDataText = match.Value;
                }
                else
                {
                    throw new Exception("分析异常!");
                }

                //文件分析结果(豆包输出)
                List<FileAnalysisDouBaoOutput> fileAnalysesContents = JsonConvert.DeserializeObject<List<FileAnalysisDouBaoOutput>>(jsonDataText);

                return fileAnalysesContents;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 构建系统提示词
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private string BuildSystemPrompt(AIGenerateQuestionsInput input)
        {
            // 根据出题模式构建不同的系统提示词
            if (input.IsKnowledgePointMode && input.KnowledgePointMapping != null && input.KnowledgePointMapping.Count > 0)
            {
                // 构建知识点映射信息
                var knowledgePointInfo = string.Join("、", input.KnowledgePointMapping.Select(kv => $"{kv.Value}(ID:{kv.Key})"));

                return $@"你是一个专业的出题助手，请根据用户要求生成题目。

当前是知识点出题模式，可选的知识点包括：{knowledgePointInfo}

要求：
1. 严格按照指定的JSON格式返回数据
2. 题型包括：单项选择题（2）、多项选择题（10）、判断题（11）、填空题（5）、主观题（36）
3. 选择题和判断题必须包含选项，填空题和主观题不需要选项
4. 单项选择题选项用A、B、C、D标识
5. 多项选择题选项用A、B、C、D标识，答案用字母组合表示，字母之间用竖线符号分割（如：A|C）
6. 判断题选项用A、B标识，A表示正确，B表示错误
7. 填空题使用中文括号格式，在题干中用（　）表示填空位置（括号内为空），答案字段填写正确答案，多个空格的答案用竖线|分隔
8. 主观题提供参考答案要点
9. 每道题必须包含详细的答案解析
10. **重要：每道题必须在KnowledgePoints数组中列出该题实际涉及的具体知识点，包含正确的ID和Content**

返回格式示例：
{{
  ""questions"": [
    {{
      ""QuestionType"": ""单项选择题"",
      ""QuestionTypeId"":2,
      ""Title"": ""这里是题干内容"",
      ""Options"": [
        {{""Option"": ""A"", ""Content"": ""选项A内容""}},
        {{""Option"": ""B"", ""Content"": ""选项B内容""}},
        {{""Option"": ""C"", ""Content"": ""选项C内容""}},
        {{""Option"": ""D"", ""Content"": ""选项D内容""}}
      ],
      ""Answer"": ""A"",
      ""Analysis"": ""这是答案解析"",
      ""KnowledgePoints"": [
        {{""Id"": ""kp001"", ""Content"": ""整数运算""}},
        {{""Id"": ""kp002"", ""Content"": ""数与代数""}}
      ]
    }},
    {{
      ""QuestionType"": ""填空题"",
        ""QuestionTypeId"":5,
      ""Title"": ""根据 9 + 6 = 15，可以写出减法算式：（15）-（9）=（6）"",
      ""Answer"": ""15|9|6"",
      ""Analysis"": ""根据加法和减法的关系，15-9=6，15-6=9"",
      ""KnowledgePoints"": [
        {{""Id"": ""kp001"", ""Content"": ""整数运算""}}
      ]
    }}
  ]
}}";
            }
            else if (input.IsChapterMode && input.ChapterMapping != null && input.ChapterMapping.Count > 0)
            {
                // 构建章节映射信息
                var chapterInfo = string.Join("、", input.ChapterMapping.Select(kv => $"{kv.Value}(ID:{kv.Key})"));

                return $@"你是一个专业的出题助手，请根据用户要求生成题目。

当前是章节出题模式，可选的章节包括：{chapterInfo}

要求：
1. 严格按照指定的JSON格式返回数据
2. 题型包括：单项选择题（2）、多项选择题（10）、判断题（11）、填空题（5）、主观题（36）
3. 选择题和判断题必须包含选项，填空题和主观题不需要选项
4. 单项选择题选项用A、B、C、D标识
5. 多项选择题选项用A、B、C、D标识，答案用字母组合表示，字母之间用竖线符号分割（如：A|C）
6. 判断题选项用A、B标识，A表示正确，B表示错误
7. 填空题使用中文括号格式，在题干中用（　）表示填空位置（括号内为空），答案字段填写正确答案，多个空格的答案用竖线|分隔
8. 主观题提供参考答案要点
9. 每道题必须包含详细的答案解析
10. **重要：每道题必须在Chapters数组中列出该题实际涉及的具体章节，包含正确的ID和ChapterName**
返回格式示例：
{{
  ""questions"": [
    {{
      ""QuestionType"": ""单项选择题"",
      ""QuestionTypeId"":2,
      ""Title"": ""这里是题干内容"",
      ""Options"": [
        {{""Option"": ""A"", ""Content"": ""选项A内容""}},
        {{""Option"": ""B"", ""Content"": ""选项B内容""}},
        {{""Option"": ""C"", ""Content"": ""选项C内容""}},
        {{""Option"": ""D"", ""Content"": ""选项D内容""}}
      ],
      ""Answer"": ""A"",
      ""Analysis"": ""这是答案解析"",
      ""Chapters"": [
        {{""Id"": ""ch001"", ""ChapterName"": ""第一章 基础知识""}}
      ]
    }},
    {{
      ""QuestionType"": ""填空题"",
      ""QuestionTypeId"":5,
      ""Title"": ""根据 9 + 6 = 15，可以写出减法算式：（　）-（　）=（　）"",
      ""Answer"": ""15|9|6"",
      ""Analysis"": ""根据加法和减法的关系，15-9=6，15-6=9"",
      ""Chapters"": [
        {{""Id"": ""ch001"", ""ChapterName"": ""第一章 基础知识""}}
      ]
    }}
  ]
}}";
            }
            else
            {
                return @"你是一个专业的出题助手，请根据用户要求生成题目。

要求：
1. 严格按照指定的JSON格式返回数据
2. 题型包括：单项选择题（2）、多项选择题（10）、判断题（11）、填空题（5）、主观题（36）
3. 选择题和判断题必须包含选项，填空题和主观题不需要选项
4. 单项选择题选项用A、B、C、D标识
5. 多项选择题选项用A、B、C、D标识，答案用字母组合表示，字母之间用竖线符号分割（如：A|C）
6. 判断题选项用A、B标识，A表示正确，B表示错误
7. 填空题使用中文括号格式，在题干中用（　）表示填空位置（括号内为空），答案字段填写正确答案，多个空格的答案用竖线|分隔
8. 主观题提供参考答案要点
9. 每道题必须包含详细的答案解析

返回格式示例：
{
  ""questions"": [
    {
      ""QuestionType"": ""单项选择题"",
      ""QuestionTypeId"":2,
      ""Title"": ""这里是题干内容"",
      ""Options"": [
        {""Option"": ""A"", ""Content"": ""选项A内容""},
        {""Option"": ""B"", ""Content"": ""选项B内容""},
        {""Option"": ""C"", ""Content"": ""选项C内容""},
        {""Option"": ""D"", ""Content"": ""选项D内容""}
      ],
      ""Answer"": ""A"",
      ""Analysis"": ""这是答案解析""
    },
    {
      ""QuestionType"": ""填空题"",
      ""QuestionTypeId"":5,
      ""Title"": ""根据 9 + 6 = 15，可以写出减法算式：（　）-（　）=（　）"",
      ""Answer"": ""15|9|6"",
      ""Analysis"": ""根据加法和减法的关系，15-9=6，15-6=9""
    }
  ]
}";
            }
        }

        /// <summary>
        /// 处理流式内容，尝试解析完整题目
        /// </summary>
        /// <param name="content">流式内容</param>
        /// <param name="parser">解析器状态</param>
        /// <param name="onProgress">进度回调</param>
        /// <returns>完整的题目对象，如果还未完整则返回null</returns>
        private async Task<AIGeneratedQuestion> ProcessStreamContent(
            string content,
            QuestionStreamParser parser,
            Func<string, Task> onProgress)
        {
            parser.Buffer.Append(content);
            var bufferContent = parser.Buffer.ToString();

            // 进度回调
            if (onProgress != null)
            {
                //await onProgress($"正在生成题目... 已接收 {bufferContent.Length} 字符");
                await Task.CompletedTask;
            }

            // 第一步：等待进入questions数组，寻找 "[" 符号
            if (!parser.IsInQuestionsArray)
            {
                var arrayStartIndex = bufferContent.IndexOf('[');
                if (arrayStartIndex >= 0)
                {
                    parser.IsInQuestionsArray = true;
                    // 从 "[" 之后开始处理
                    bufferContent = bufferContent.Substring(arrayStartIndex + 1);
                    parser.Buffer.Clear();
                    parser.Buffer.Append(bufferContent);
                    Console.WriteLine("进入questions数组，开始解析题目");
                }
                else
                {
                    // 还没找到数组开始，继续等待
                    return null;
                }
            }

            // 第二步：在数组内寻找完整的题目对象
            if (parser.IsInQuestionsArray)
            {
                // 寻找题目开始的 "{"
                if (!parser.IsInQuestionBlock)
                {
                    var questionStartIndex = bufferContent.IndexOf('{');
                    if (questionStartIndex >= 0)
                    {
                        parser.IsInQuestionBlock = true;
                        // 从 "{" 开始计算
                        bufferContent = bufferContent.Substring(questionStartIndex);
                        parser.Buffer.Clear();
                        parser.Buffer.Append(bufferContent);
                        parser.BraceCount = 1; // 初始化为1，因为我们已经遇到了开始的 "{"
                        Console.WriteLine("找到题目开始，开始计算大括号平衡");
                    }
                    else
                    {
                        // 还没找到题目开始，继续等待
                        return null;
                    }
                }

                // 第三步：计算大括号平衡，寻找 "}," 模式
                if (parser.IsInQuestionBlock)
                {
                    // 重新计算整个缓冲区的大括号平衡
                    parser.BraceCount = 0;
                    foreach (char c in bufferContent)
                    {
                        if (c == '{') parser.BraceCount++;
                        else if (c == '}') parser.BraceCount--;
                    }

                    Console.WriteLine($"当前大括号计数: {parser.BraceCount}, 缓冲区长度: {bufferContent.Length}");

                    // 检查是否以 "}," 结尾且大括号平衡
                    if (parser.BraceCount == 0 && (bufferContent.TrimEnd().EndsWith("},") || bufferContent.TrimEnd().EndsWith("}")))
                    {
                        try
                        {
                            // 获取完整的题目JSON
                            var questionJson = bufferContent.Trim();

                            // 移除尾部的逗号（如果有）
                            if (questionJson.EndsWith(","))
                            {
                                questionJson = questionJson.Substring(0, questionJson.Length - 1);
                            }

                            Console.WriteLine($"尝试解析题目JSON: {questionJson}");

                            var question = JsonConvert.DeserializeObject<AIGeneratedQuestion>(questionJson);

                            //if (rawQuestion != null && !string.IsNullOrEmpty(rawQuestion.question))
                            //{
                            //    // 转换为标准格式
                            //    var question = ConvertRawQuestionToStandard(rawQuestion);
                            //    Console.WriteLine($"成功解析第 {parser.CurrentQuestionIndex + 1} 题: {question.Title}");
                            return question;
                            //}
                        }
                        catch (Newtonsoft.Json.JsonException ex)
                        {
                            Console.WriteLine($"JSON解析失败: {ex.Message}");
                            Console.WriteLine($"失败的JSON内容: {bufferContent}");
                            // JSON解析失败，可能还不完整，继续累积
                        }
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 转换原始AI题目格式为标准格式
        /// </summary>
        /// <param name="rawQuestion">原始AI题目</param>
        /// <returns>标准格式题目</returns>
        private AIGeneratedQuestion ConvertRawQuestionToStandard(RawAIQuestion rawQuestion)
        {
            var question = new AIGeneratedQuestion
            {
                Title = rawQuestion.question,
                Answer = rawQuestion.answer,
                Analysis = rawQuestion.analysis,
                QuestionTypeId = rawQuestion.type?.ToString(),
                QuestionType = GetQuestionTypeName(rawQuestion.type)
            };

            // 转换选项格式
            if (rawQuestion.options != null)
            {
                question.Options = new List<AIQuestionOption>();
                foreach (var option in rawQuestion.options)
                {
                    question.Options.Add(new AIQuestionOption
                    {
                        Option = option.Key,
                        Content = option.Value
                    });
                }
            }

            return question;
        }

        /// <summary>
        /// 根据题型ID获取题型名称
        /// </summary>
        /// <param name="typeId">题型ID</param>
        /// <returns>题型名称</returns>
        private string GetQuestionTypeName(int? typeId)
        {
            return typeId switch
            {
                2 => "单项选择题",
                10 => "多项选择题",
                11 => "判断题",
                5 => "填空题",
                36 => "主观题",
                _ => "未知题型"
            };
        }
    }

    /// <summary>
    /// AI返回的原始题目格式
    /// </summary>
    public class RawAIQuestion
    {
        /// <summary>
        /// 题型ID
        /// </summary>
        public int? type { get; set; }

        /// <summary>
        /// 题干
        /// </summary>
        public string question { get; set; }

        /// <summary>
        /// 选项（原始格式：对象）
        /// </summary>
        public Dictionary<string, string> options { get; set; }

        /// <summary>
        /// 正确答案
        /// </summary>
        public string answer { get; set; }

        /// <summary>
        /// 答案解析
        /// </summary>
        public string analysis { get; set; }
    }
}
