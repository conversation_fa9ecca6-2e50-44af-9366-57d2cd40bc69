﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <Nullable>disable</Nullable>
    <UserSecretsId>a3e3c6fb-4b7e-44a6-8dbe-5e5f11af4202</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>bin\Debug\netcoreapp6.0\</OutputPath>
    <NoWarn>1701;1702;CS1591;CS8618;CS8600;CS8602;CS1572;CS1573;CS1587;CS1591;CS1570;CS8765;CA2200;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DocumentationFile>bin\Release\netcoreapp6.0\Uwoo.WebApi.xml</DocumentationFile>
    <OutputPath>bin\Release\netcoreapp6.0\</OutputPath>
    <NoWarn>1701;1702;CS1572;CS1573;CS1587;CS8618;CS8600;CS8602;CS1591;CS1570;CS8765;NU1902;NU1903;NU1904;</NoWarn>
  </PropertyGroup>
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<!-- 启用 XML 文档生成 -->
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<!-- 这里定义了生成的 XML 文件名！ -->
		<DocumentationFile>UwooAgent.WebApi.xml</DocumentationFile>
		<!-- 忽略未文档化的警告（如缺少 XML 注释） -->
		<NoWarn>$(NoWarn);1591</NoWarn>
		<!-- 强制 XML 文件编码为 UTF-8 with BOM -->
		<FileEncoding>utf-8-bom</FileEncoding>
		<ProduceReferenceAssembly>True</ProduceReferenceAssembly>
	</PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.27" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.15.1" />
    <PackageReference Include="Quartz" Version="3.4.0" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.112" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Uwoo.Builder\UwooAgent.Builder.csproj" />
    <ProjectReference Include="..\Uwoo.Core\UwooAgent.Core.csproj" />
    <ProjectReference Include="..\Uwoo.Entity\UwooAgent.Entity.csproj" />
    <ProjectReference Include="..\Uwoo.Mongo\UwooAgent.Mongo.csproj" />
    <ProjectReference Include="..\Uwoo.System\UwooAgent.System.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Download\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="nlog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>


</Project>
