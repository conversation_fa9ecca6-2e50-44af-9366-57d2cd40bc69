<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
        <DocumentationFile>Uwoo.Mongo.xml</DocumentationFile>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Autofac" Version="4.9.4" />
        <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="5.0.1" />
        <PackageReference Include="Autofac.Extras.DynamicProxy" Version="4.5.0" />
        <PackageReference Include="CSRedisCore" Version="3.6.5" />
        <PackageReference Include="Dapper" Version="2.0.143" />
        <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.1" />
        <PackageReference Include="MongoDB.Driver" Version="2.22.0" />
        <PackageReference Include="Masa.Contrib.Data.IdGenerator.Snowflake" Version="1.0.0" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\Uwoo.Contracts\UwooAgent.Contracts.csproj" />
    </ItemGroup>

</Project>
